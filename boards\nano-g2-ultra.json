{"build": {"arduino": {"ldscript": "nrf52840_s140_v6.ld"}, "core": "nRF5", "cpu": "cortex-m4", "extra_flags": "-DARDUINO_NRF52840_FEATHER -DNRF52840_XXAA", "f_cpu": "64000000L", "hwids": [["0x239A", "0x8029"], ["0x239A", "0x0029"], ["0x239A", "0x002A"], ["0x239A", "0x802A"]], "usb_product": "BQ nRF52840", "mcu": "nrf52840", "variant": "nano-g2-ultra", "bsp": {"name": "adafruit"}, "softdevice": {"sd_flags": "-DS140", "sd_name": "s140", "sd_version": "6.1.1", "sd_fwid": "0x00B6"}, "bootloader": {"settings_addr": "0xFF000"}}, "connectivity": ["bluetooth"], "debug": {"jlink_device": "nRF52840_xxAA", "svd_path": "nrf52840.svd"}, "frameworks": ["a<PERSON><PERSON><PERSON>"], "name": "BQ nRF52840", "upload": {"maximum_ram_size": 248832, "maximum_size": 815104, "speed": 115200, "protocol": "nrfutil", "protocols": ["jlink", "nrfjprog", "nrfutil", "stlink"], "use_1200bps_touch": true, "require_upload_port": true, "wait_for_upload_port": true}, "url": "https://wiki.uniteng.com/en/meshtastic/nano-g2-ultra", "vendor": "BQ Consulting"}