[esp32c6_base]
extends = esp32_base
platform = https://github.com/Jason2866/platform-espressif32/archive/22faa566df8c789000f8136cd8d0aca49617af55.zip
build_flags =
  ${arduino_base.build_flags}
  -Wall
  -Wextra
  -Isrc/platform/esp32
  -std=c++11
  -DESP_OPENSSL_SUPPRESS_LEGACY_WARNING
  -DSERIAL_BUFFER_SIZE=4096
  -DLIBPAX_ARDUINO
  -DLIBPAX_WIFI
  -DLIBPAX_BLE
  -DMESHTASTIC_EXCLUDE_WEBSERVER
  ;-DDEBUG_HEAP
  ; TEMP
  -DHAS_BLUETOOTH=0
  -DMESHTASTIC_EXCLUDE_PAXCOUNTER
  -DMESHTASTIC_EXCLUDE_BLUETOOTH

lib_deps =
  ${arduino_base.lib_deps}
  ${networking_base.lib_deps}
  ${environmental_base.lib_deps}
  ${radiolib_base.lib_deps}
  lewisxhe/XPowersLib@^0.2.7
  https://github.com/meshtastic/ESP32_Codec2/archive/633326c78ac251c059ab3a8c430fcdf25b41672f.zip
  rweather/Crypto@^0.4.0

build_src_filter = 
 ${esp32_base.build_src_filter} -<mesh/http> 

monitor_speed = 460800
monitor_filters = esp32_c3_exception_decoder

lib_ignore =
  NonBlockingRTTTL
  NimBLE-Arduino
  libpax
  
