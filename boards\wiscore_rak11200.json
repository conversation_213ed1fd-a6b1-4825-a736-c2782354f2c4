{"build": {"arduino": {"ldscript": "esp32_out.ld"}, "core": "esp32", "extra_flags": ["-DBOARD_HAS_PSRAM", "-DARDUINO_ESP32_DEV"], "f_cpu": "240000000L", "f_flash": "40000000L", "flash_mode": "dio", "mcu": "esp32", "variant": "WisCore_RAK11200_Board"}, "connectivity": ["wifi", "bluetooth", "ethernet", "can"], "frameworks": ["a<PERSON><PERSON><PERSON>", "espidf"], "name": "WisCore RAK11200 Board", "upload": {"flash_size": "4MB", "maximum_ram_size": 327680, "maximum_size": 4194304, "protocols": ["esptool", "espota", "ftdi"], "require_upload_port": true, "speed": 460800}, "url": "https://www.rakwireless.com", "vendor": "RAKwireless"}