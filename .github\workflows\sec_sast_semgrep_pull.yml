---
name: Semgrep Differential Scan
on: pull_request

permissions: read-all

jobs:
  semgrep-diff:
    runs-on: ubuntu-22.04
    container:
      image: semgrep/semgrep

    steps:
      # step 1
      - name: clone application source code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      # step 2
      - name: differential scan
        run: |
          semgrep scan \
            --error \
            --metrics=off \
            --baseline-commit ${{ github.event.pull_request.base.sha }} \
            --config="p/default"
