{"build": {"arduino": {"ldscript": "nrf52840_s140_v6.ld"}, "core": "nRF5", "cpu": "cortex-m4", "extra_flags": "-DARDUINO_NRF52840_TTGO_EINK -DNRF52840_XXAA", "f_cpu": "64000000L", "hwids": [["0x239A", "0x4405"]], "usb_product": "TTGO_eink", "mcu": "nrf52840", "variant": "eink0.1", "variants_dir": "variants", "bsp": {"name": "adafruit"}, "softdevice": {"sd_flags": "-DS140", "sd_name": "s140", "sd_version": "6.1.1", "sd_fwid": "0x00B6"}, "bootloader": {"settings_addr": "0xFF000"}}, "connectivity": ["bluetooth"], "debug": {"jlink_device": "nRF52840_xxAA", "onboard_tools": ["jlink"], "svd_path": "nrf52840.svd", "openocd_target": "nrf52840-mdk-rs"}, "frameworks": ["a<PERSON><PERSON><PERSON>"], "name": "TTGO eink (Adafruit BSP)", "upload": {"maximum_ram_size": 248832, "maximum_size": 815104, "require_upload_port": true, "speed": 115200, "protocol": "jlink", "protocols": ["jlink", "nrfjprog", "stlink"]}, "url": "FIXME", "vendor": "TTGO"}