name: Setup Build Base Composite Action
description: Base build actions for Meshtastic Platform IO steps

runs:
  using: composite
  steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        submodules: recursive
        ref: ${{github.event.pull_request.head.ref}}
        repository: ${{github.event.pull_request.head.repo.full_name}}

    - name: Uncomment build epoch
      shell: bash
      run: |
        sed -i 's/#-DBUILD_EPOCH=$UNIX_TIME/-DBUILD_EPOCH=$UNIX_TIME/' platformio.ini

    - name: Install dependencies
      shell: bash
      run: |
        sudo apt-get -y update --fix-missing
        sudo apt-get install -y cppcheck libbluetooth-dev libgpiod-dev libyaml-cpp-dev lsb-release

    - name: Setup Python
      uses: actions/setup-python@v5
      with:
        python-version: 3.x
        cache: pip
        cache-dependency-path: |
          .github/actions/**
          **.ini

    - name: Upgrade python tools
      shell: bash
      run: |
        python -m pip install --upgrade pip
        pip install -U --no-build-isolation --no-cache-dir "setuptools<72"
        pip install -U platformio adafruit-nrfutil --no-build-isolation
        pip install -U poetry --no-build-isolation
        pip install -U meshtastic --pre --no-build-isolation

    - name: Upgrade platformio
      shell: bash
      run: |
        pio upgrade
