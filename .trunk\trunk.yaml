version: 0.1
cli:
  version: 1.22.11
plugins:
  sources:
    - id: trunk
      ref: v1.6.7
      uri: https://github.com/trunk-io/plugins
lint:
  enabled:
    - prettier@3.5.3
    - trufflehog@3.88.20
    - yamllint@1.37.0
    - bandit@1.8.3
    - checkov@3.2.394
    - terrascan@1.19.9
    - trivy@0.61.0
    - taplo@0.9.3
    - ruff@0.11.2
    - isort@6.0.1
    - markdownlint@0.44.0
    - oxipng@9.1.4
    - svgo@3.3.2
    - actionlint@1.7.7
    - flake8@7.1.2
    - hadolint@2.12.1-beta
    - shfmt@3.6.0
    - shellcheck@0.10.0
    - black@25.1.0
    - git-diff-check
    - gitleaks@8.24.2
    - clang-format@16.0.3
  ignore:
    - linters: [ALL]
      paths:
        - bin/**
runtimes:
  enabled:
    - python@3.10.8
    - go@1.21.0
    - node@18.20.5
actions:
  disabled:
    - trunk-announce
  enabled:
    - trunk-fmt-pre-commit
    - trunk-check-pre-push
    - trunk-upgrade-available
