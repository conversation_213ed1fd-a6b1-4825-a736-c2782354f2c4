{"build": {"arduino": {"ldscript": "nrf52840_s140_v7.ld"}, "core": "nRF5", "cpu": "cortex-m4", "extra_flags": "-DARDUINO_MDBT50Q_RX -DNRF52840_XXAA", "f_cpu": "64000000L", "hwids": [["0x239A", "0x810B"], ["0x239A", "0x010B"], ["0x239A", "0x810C"]], "usb_product": "XIAO-BOOT", "mcu": "nrf52840", "variant": "Seeed_XIAO_nRF52840_Sense", "bsp": {"name": "adafruit"}, "softdevice": {"sd_flags": "-DS140", "sd_name": "s140", "sd_version": "7.3.0", "sd_fwid": "0x0123"}, "bootloader": {"settings_addr": "0xFF000"}}, "connectivity": ["bluetooth"], "debug": {"jlink_device": "nRF52840_xxAA", "svd_path": "nrf52840.svd", "openocd_target": "nrf52840-mdk-rs"}, "frameworks": ["a<PERSON><PERSON><PERSON>"], "name": "Seeed <PERSON> BLE <PERSON>", "upload": {"maximum_ram_size": 248832, "maximum_size": 815104, "speed": 115200, "protocol": "nrfutil", "protocols": ["jlink", "nrfjprog", "nrfutil", "stlink", "cmsis-dap", "blackmagic"], "use_1200bps_touch": true, "require_upload_port": true, "wait_for_upload_port": true}, "url": "https://www.seeedstudio.com/Seeed-XIAO-BLE-Sense-nRF52840-p-5253.html", "vendor": "Seeed Studio"}