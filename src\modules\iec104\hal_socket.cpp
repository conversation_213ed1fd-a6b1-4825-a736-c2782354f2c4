/*
 *  socket_w5500.cpp
 *
 *  Copyright 2024
 *
 *  This file is part of Platform Abstraction Layer (libpal)
 *  for libiec61850, libmms, and lib60870.
 *
 *  W5500 Ethernet controller socket implementation
 */

#include <Arduino.h>
#include <stdbool.h>
#include <stdio.h>
#include <string.h>

// Include W5500 Ethernet library
#include "Ethernet.h"
#include "utility/w5100.h"
#include "DebugConfiguration.h"
extern "C" {
#include "hal_socket.h"
#include "lib_memory.h"
}

#ifndef DEBUG_SOCKET
#define DEBUG_SOCKET 1
#endif

// Maximum number of sockets supported by W5500
#ifndef MAX_SOCK_NUM
#define MAX_SOCK_NUM 8
#endif

// Define socket usage types
typedef enum {
    SOCKET_UNUSED = 0,
    SOCKET_LISTENING,
    SOCKET_CONNECTED
} SocketUsage;

// Connection table to track socket usage
static SocketUsage socketUsageTable[MAX_SOCK_NUM] = {SOCKET_UNUSED};

struct sSocket
{
    uint8_t sockindex;
    uint32_t connectTimeout;
    bool isConnected;
};

struct sServerSocket
{
    uint8_t sockindex;
    int backLog;
    uint16_t port;
};

struct sHandleSet
{
    uint8_t sockets[MAX_SOCK_NUM];
    int count;
};

struct sUdpSocket
{
    uint8_t sockindex;
    uint16_t localPort;
};

// Global initialization flag
static bool w5500Initialized = false;
static int socketCount = 0;

HandleSet
Handleset_new(void)
{
    HandleSet result = (HandleSet)GLOBAL_MALLOC(sizeof(struct sHandleSet));
    
    if (result != NULL) {
        result->count = 0;
        memset(result->sockets, MAX_SOCK_NUM, sizeof(result->sockets));
    }
    
    return result;
}

void
Handleset_reset(HandleSet self)
{
    if (self != NULL) {
        self->count = 0;
        memset(self->sockets, MAX_SOCK_NUM, sizeof(self->sockets));
    }
}

void
Handleset_addSocket(HandleSet self, const Socket sock)
{
    if (self != NULL && sock != NULL && sock->sockindex < MAX_SOCK_NUM) {
        // Check if socket is not already in the set
        for (int i = 0; i < self->count; i++) {
            if (self->sockets[i] == sock->sockindex) {
                return; // Already in set
            }
        }
        
        if (self->count < MAX_SOCK_NUM) {
            self->sockets[self->count] = sock->sockindex;
            self->count++;
        }
    }
}

void
Handleset_removeSocket(HandleSet self, const Socket sock)
{
    if (self != NULL && sock != NULL && sock->sockindex < MAX_SOCK_NUM) {
        for (int i = 0; i < self->count; i++) {
            if (self->sockets[i] == sock->sockindex) {
                // Remove socket by shifting remaining elements
                for (int j = i; j < self->count - 1; j++) {
                    self->sockets[j] = self->sockets[j + 1];
                }
                self->count--;
                break;
            }
        }
    }
}

int
Handleset_waitReady(HandleSet self, unsigned int timeoutMs)
{
    if (self == NULL || self->count == 0) {
        return -1;
    }
    
    unsigned long startTime = millis();
    int readyCount = 0;
    
    while ((millis() - startTime) < timeoutMs) {
        readyCount = 0;
        
        // Check each socket for available data
        for (int i = 0; i < self->count; i++) {
            uint8_t sockindex = self->sockets[i];
            if (sockindex < MAX_SOCK_NUM) {
                uint16_t available = Ethernet.socketRecvAvailable(sockindex);
                if (available > 0) {
                    readyCount++;
                }
            }
        }
        
        if (readyCount > 0) {
            break;
        }
        
        delay(1); // Small delay to prevent busy waiting
    }
    
    return readyCount;
}

void
Handleset_destroy(HandleSet self)
{
    if (self != NULL) {
        GLOBAL_FREEMEM(self);
    }
}

void
Socket_activateTcpKeepAlive(Socket self, int idleTime, int interval, int count)
{
    // W5500 doesn't have direct TCP keepalive configuration
    // This is a no-op for W5500 implementation
    (void)self;
    (void)idleTime;
    (void)interval;
    (void)count;
    
    if (DEBUG_SOCKET)
        LOG_DEBUG("[W5500_SOCKET] TCP keepalive not supported by W5500");
}

ServerSocket
TcpServerSocket_create(const char* address, int port)
{
    ServerSocket serverSocket = (ServerSocket)GLOBAL_MALLOC(sizeof(struct sServerSocket));
    if (serverSocket == NULL) {
        return NULL;
    }
    
    // Get a socket from W5500
    uint8_t sockindex = Ethernet.socketBegin(SnMR::TCP, port);
    if (sockindex >= MAX_SOCK_NUM) {
        if (DEBUG_SOCKET)
            LOG_DEBUG("[W5500_SOCKET] No available sockets for server");
        GLOBAL_FREEMEM(serverSocket);
        return NULL;
    }
    
    serverSocket->sockindex = sockindex;
    serverSocket->backLog = 10; // Default backlog
    serverSocket->port = port;
    
    socketCount++;
    if (DEBUG_SOCKET)
        LOG_DEBUG("[W5500_SOCKET] Server socket created on port %d, socket %d", port, sockindex);
    
    return serverSocket;
}

void
ServerSocket_listen(ServerSocket self)
{
    if (self != NULL && self->sockindex < MAX_SOCK_NUM) {
        Ethernet.socketListen(self->sockindex);
        socketUsageTable[self->sockindex] = SOCKET_LISTENING; // Mark this socket as listening
        
        if (DEBUG_SOCKET)
            LOG_DEBUG("[W5500_SOCKET] Server socket %d listening", self->sockindex);
    }
    else{
        LOG_DEBUG("[W5500_SOCKET] socketListen failed = %d", self->sockindex);
    }
}

Socket
ServerSocket_accept(ServerSocket self)
{
    if (self == NULL) {
        return NULL;
    }
    
    // 如果服务器socket已经被使用，需要重新创建监听socket
    if (self->sockindex >= MAX_SOCK_NUM) {
        uint8_t newSockindex = Ethernet.socketBegin(SnMR::TCP, self->port);
        
        // 如果直接创建失败，尝试检查是否有已经关闭的socket可以复用
        if (newSockindex >= MAX_SOCK_NUM) {
            if (DEBUG_SOCKET)
                LOG_DEBUG("[W5500_SOCKET] No available sockets, checking for closed sockets");
                
            // 检查所有socket，找到状态为CLOSED的socket
            for (int i = 0; i < MAX_SOCK_NUM; i++) {
                uint8_t status = Ethernet.socketStatus(i);
                if (status == SnSR::CLOSED && socketUsageTable[i] != SOCKET_LISTENING) {
                    // 找到一个已关闭且非监听的socket，尝试使用它
                    if (DEBUG_SOCKET)
                        LOG_DEBUG("[W5500_SOCKET] Found closed socket %d, trying to reuse", i);
                    
                    // 先关闭这个socket以确保它完全释放
                    Ethernet.socketClose(i);
                    
                    // 现在尝试在这个socket上创建新的监听socket
                    newSockindex = Ethernet.socketBegin(SnMR::TCP, self->port);
                    if (newSockindex == i) {
                        // 成功复用了socket
                        break;
                    }
                }
            }
        }
        
        if (newSockindex >= MAX_SOCK_NUM) {
            if (DEBUG_SOCKET)
                LOG_DEBUG("[W5500_SOCKET] Failed to recreate server socket after trying all options");
            return NULL;
        }
        
        self->sockindex = newSockindex;
        Ethernet.socketListen(self->sockindex);
        socketUsageTable[self->sockindex] = SOCKET_LISTENING; // Mark this socket as listening
        
        if (DEBUG_SOCKET)
            LOG_DEBUG("[W5500_SOCKET] Server listen socket recreated on port %d, socket %d", self->port, self->sockindex);
    }
    
    uint8_t status = Ethernet.socketStatus(self->sockindex);
    
    // Check if there's an incoming connection
    if (status == SnSR::ESTABLISHED) {
        Socket conSocket = (Socket)GLOBAL_CALLOC(1, sizeof(struct sSocket));
        if (conSocket == NULL) {
            LOG_DEBUG("[W5500_SOCKET] conSocket malloc failed ");
            return NULL;
        }
        
        // 使用当前的服务器socket作为连接socket
        conSocket->sockindex = self->sockindex;
        conSocket->connectTimeout = 5000;
        conSocket->isConnected = true;
        
        // 取消标记这个socket为监听，因为它现在用于连接
        socketUsageTable[self->sockindex] = SOCKET_CONNECTED; // Mark this socket as connected
        
        socketCount++;
        
        if (DEBUG_SOCKET)
        LOG_DEBUG("[W5500_SOCKET] Connection accepted on socket %d", conSocket->sockindex);
        
        // 服务器socket已经用于连接，标记为需要重新创建
        self->sockindex = MAX_SOCK_NUM;
        
        return conSocket;
    }
    
    return NULL;
}

void
ServerSocket_setBacklog(ServerSocket self, int backlog)
{
    if (self != NULL) {
        self->backLog = backlog;
    }
}

void
ServerSocket_destroy(ServerSocket self)
{
    if (self != NULL) {
        if (self->sockindex < MAX_SOCK_NUM) {
            // Clear the listening flag before closing
            socketUsageTable[self->sockindex] = SOCKET_UNUSED;
            Ethernet.socketClose(self->sockindex);
            socketCount--;
        }
        GLOBAL_FREEMEM(self);
    }
}

Socket
TcpSocket_create()
{
    Socket self = (Socket)GLOBAL_MALLOC(sizeof(struct sSocket));
    if (self == NULL) {
        return NULL;
    }
    
    // Initialize socket but don't open it yet
    self->sockindex = MAX_SOCK_NUM; // Mark as not opened
    self->connectTimeout = 5000;
    self->isConnected = false;
    
    if (DEBUG_SOCKET)
        LOG_DEBUG("[W5500_SOCKET] TCP socket created");
    
    return self;
}

void
Socket_setConnectTimeout(Socket self, uint32_t timeoutInMs)
{
    if (self != NULL) {
        self->connectTimeout = timeoutInMs;
    }
}

bool
Socket_bind(Socket self, const char* srcAddress, int srcPort)
{
    // W5500 doesn't support explicit binding for client sockets
    // This is typically handled automatically by the W5500 hardware
    (void)self;
    (void)srcAddress;
    (void)srcPort;

    if (DEBUG_SOCKET)
        LOG_DEBUG("[W5500_SOCKET] Socket bind not explicitly supported by W5500");

    return true; // Return true as W5500 handles this automatically
}

bool
Socket_connectAsync(Socket self, const char* address, int port)
{
    if (self == NULL) {
        return false;
    }

    // Parse IP address
    IPAddress ip;
    if (!ip.fromString(address)) {
        if (DEBUG_SOCKET)
            LOG_DEBUG("[W5500_SOCKET] Invalid IP address: %s", address);
        return false;
    }

    // Get a socket if not already allocated
    if (self->sockindex >= MAX_SOCK_NUM) {
        self->sockindex = Ethernet.socketBegin(SnMR::TCP, 0);
        if (self->sockindex >= MAX_SOCK_NUM) {
            if (DEBUG_SOCKET)
                LOG_DEBUG("[W5500_SOCKET] No available sockets");
            return false;
        }
        socketUsageTable[self->sockindex] = SOCKET_CONNECTED; // Mark as connected
        socketCount++;
    }

    // Start connection
    Ethernet.socketConnect(self->sockindex, ip.raw_address(), port);

    if (DEBUG_SOCKET)
        LOG_DEBUG("[W5500_SOCKET] Async connect started to %s:%d on socket %d",
               address, port, self->sockindex);

    return true;
}

SocketState
Socket_checkAsyncConnectState(Socket self)
{
    if (self == NULL || self->sockindex >= MAX_SOCK_NUM) {
        return SOCKET_STATE_FAILED;
    }

    uint8_t status = Ethernet.socketStatus(self->sockindex);

    switch (status) {
        case SnSR::ESTABLISHED:
            self->isConnected = true;
            return SOCKET_STATE_CONNECTED;

        case SnSR::SYNSENT:
        case SnSR::SYNRECV:
            return SOCKET_STATE_CONNECTING;

        case SnSR::CLOSED:
        case SnSR::CLOSE_WAIT:
        case SnSR::FIN_WAIT:
        case SnSR::CLOSING:
        case SnSR::TIME_WAIT:
        case SnSR::LAST_ACK:
            return SOCKET_STATE_FAILED;

        default:
            return SOCKET_STATE_CONNECTING;
    }
}

bool
Socket_connect(Socket self, const char* address, int port)
{
    if (!Socket_connectAsync(self, address, port)) {
        return false;
    }

    // Wait for connection with timeout
    unsigned long startTime = millis();

    while ((millis() - startTime) < self->connectTimeout) {
        SocketState state = Socket_checkAsyncConnectState(self);

        if (state == SOCKET_STATE_CONNECTED) {
            return true;
        } else if (state == SOCKET_STATE_FAILED) {
            return false;
        }

        delay(10); // Small delay
    }

    // Timeout
    if (DEBUG_SOCKET)
        LOG_DEBUG("[W5500_SOCKET] Connection timeout");

    Ethernet.socketClose(self->sockindex);
    socketUsageTable[self->sockindex] = SOCKET_UNUSED; // Mark as unused
    self->sockindex = MAX_SOCK_NUM;
    self->isConnected = false;

    return false;
}

char*
Socket_getPeerAddress(Socket self)
{
    if (self == NULL || self->sockindex >= MAX_SOCK_NUM || !self->isConnected) {
        return NULL;
    }

    // W5500 doesn't provide direct peer address access
    // Return a placeholder for now
    char* peerAddr = (char*)GLOBAL_MALLOC(32);
    if (peerAddr != NULL) {
        strcpy(peerAddr, "unknown:0");
    }

    return peerAddr;
}

char*
Socket_getLocalAddress(Socket self)
{
    if (self == NULL || self->sockindex >= MAX_SOCK_NUM) {
        return NULL;
    }

    // Get local IP from Ethernet class
    IPAddress localIP = Ethernet.localIP();

    char* localAddr = (char*)GLOBAL_MALLOC(32);
    if (localAddr != NULL) {
        LOG_DEBUG(localAddr, "[W5500_SOCKET] %d.%d.%d.%d:0", localIP[0], localIP[1], localIP[2], localIP[3]);
    }

    return localAddr;
}

char*
Socket_getPeerAddressStatic(Socket self, char* peerAddressString)
{
    if (self == NULL || self->sockindex >= MAX_SOCK_NUM ||
        !self->isConnected || peerAddressString == NULL) {
        return NULL;
    }

    // W5500 doesn't provide direct peer address access
    strcpy(peerAddressString, "unknown:0");

    return peerAddressString;
}

int
Socket_read(Socket self, uint8_t* buf, int size)
{
    if (self == NULL || self->sockindex >= MAX_SOCK_NUM || buf == NULL) {
        return -1;
    }

    // Check if socket is connected
    uint8_t status = Ethernet.socketStatus(self->sockindex);
    if (status != SnSR::ESTABLISHED && status != SnSR::CLOSE_WAIT) {
        self->isConnected = false;
        return -1; // Connection closed
    }

    int bytesRead = Ethernet.socketRecv(self->sockindex, buf, size);

    if (bytesRead == 0) {
        // Check if connection is still alive
        if (status == SnSR::CLOSE_WAIT || status == SnSR::CLOSED) {
            self->isConnected = false;
            return -1; // Connection closed
        }
        return 0; // No data available but connection is alive
    }

    return bytesRead;
}

int
Socket_write(Socket self, uint8_t* buf, int size)
{
    if (self == NULL || self->sockindex >= MAX_SOCK_NUM || buf == NULL) {
        return -1;
    }

    // Check if socket is connected
    uint8_t status = Ethernet.socketStatus(self->sockindex);
    if (status != SnSR::ESTABLISHED && status != SnSR::CLOSE_WAIT) {
        self->isConnected = false;
        return -1;
    }

    uint16_t bytesSent = Ethernet.socketSend(self->sockindex, buf, size);

    if (bytesSent == 0) {
        return -1; // Error occurred
    }

    return (int)bytesSent;
}

void
Socket_destroy(Socket self)
{
    if (self != NULL) {
        if (self->sockindex < MAX_SOCK_NUM) {
            // Don't close if this is a listening socket
            if (socketUsageTable[self->sockindex] != SOCKET_LISTENING) {
                Ethernet.socketClose(self->sockindex);
                LOG_DEBUG("[W5500_SOCKET] socketClose %d",self->sockindex);
                socketCount--;
                // Mark socket as unused
                socketUsageTable[self->sockindex] = SOCKET_UNUSED;
            }
            else {
                if (DEBUG_SOCKET)
                    LOG_DEBUG("[W5500_SOCKET] Socket %d is listening, not closing", self->sockindex);
            }
        }
        GLOBAL_FREEMEM(self);
    }
}

// UDP Socket Implementation

UdpSocket
UdpSocket_create(void)
{
    UdpSocket self = (UdpSocket)GLOBAL_MALLOC(sizeof(struct sUdpSocket));
    if (self == NULL) {
        return NULL;
    }

    // Initialize UDP socket but don't open it yet
    self->sockindex = MAX_SOCK_NUM; // Mark as not opened
    self->localPort = 0;

    if (DEBUG_SOCKET)
        LOG_DEBUG("[W5500_SOCKET] UDP socket created");

    return self;
}

UdpSocket
UdpSocket_createIpV6(void)
{
    // W5500 doesn't support IPv6, return NULL
    if (DEBUG_SOCKET)
        LOG_DEBUG("[W5500_SOCKET] IPv6 not supported by W5500");

    return NULL;
}

bool
UdpSocket_addGroupMembership(UdpSocket self, const char* multicastAddress)
{
    if (self == NULL || multicastAddress == NULL) {
        return false;
    }

    // Parse multicast IP address
    IPAddress ip;
    if (!ip.fromString(multicastAddress)) {
        if (DEBUG_SOCKET)
            LOG_DEBUG("[W5500_SOCKET] Invalid multicast address: %s", multicastAddress);
        return false;
    }

    // Get a socket if not already allocated
    if (self->sockindex >= MAX_SOCK_NUM) {
        self->sockindex = Ethernet.socketBeginMulticast(SnMR::UDP, ip, self->localPort);
        if (self->sockindex >= MAX_SOCK_NUM) {
            if (DEBUG_SOCKET)
                LOG_DEBUG("[W5500_SOCKET] No available sockets for multicast");
            return false;
        }
        // UDP sockets are not marked as connected or listening, they're just used for UDP
        socketUsageTable[self->sockindex] = SOCKET_UNUSED; // UDP sockets don't use the connection table the same way
        socketCount++;
    }

    if (DEBUG_SOCKET)
        LOG_DEBUG("[W5500_SOCKET] Joined multicast group %s", multicastAddress);

    return true;
}

bool
UdpSocket_setMulticastTtl(UdpSocket self, int ttl)
{
    // W5500 doesn't provide direct TTL control
    // This is a no-op for W5500 implementation
    (void)self;
    (void)ttl;

    if (DEBUG_SOCKET)
        LOG_DEBUG("[W5500_SOCKET] Multicast TTL control not supported by W5500");

    return true; // Return true to indicate no error
}

bool
UdpSocket_bind(UdpSocket self, const char* address, int port)
{
    if (self == NULL) {
        return false;
    }

    // Get a socket if not already allocated
    if (self->sockindex >= MAX_SOCK_NUM) {
        self->sockindex = Ethernet.socketBegin(SnMR::UDP, port);
        if (self->sockindex >= MAX_SOCK_NUM) {
            if (DEBUG_SOCKET)
                LOG_DEBUG("[W5500_SOCKET] No available sockets for UDP bind");
            return false;
        }
        // UDP sockets are not marked as connected or listening, they're just used for UDP
        socketUsageTable[self->sockindex] = SOCKET_UNUSED; // UDP sockets don't use the connection table the same way
        socketCount++;
    }

    self->localPort = port;

    if (DEBUG_SOCKET)
        LOG_DEBUG("[W5500_SOCKET] UDP socket bound to port %d, socket %d", port, self->sockindex);

    return true;
}

bool
UdpSocket_sendTo(UdpSocket self, const char* address, int port, uint8_t* msg, int msgSize)
{
    if (self == NULL || self->sockindex >= MAX_SOCK_NUM ||
        address == NULL || msg == NULL || msgSize <= 0) {
        return false;
    }

    // Parse IP address
    IPAddress ip;
    if (!ip.fromString(address)) {
        if (DEBUG_SOCKET)
            LOG_DEBUG("[W5500_SOCKET] Invalid IP address: %s", address);
        return false;
    }

    // Start UDP packet
    if (!Ethernet.socketStartUDP(self->sockindex, ip.raw_address(), port)) {
        if (DEBUG_SOCKET)
            LOG_DEBUG("[W5500_SOCKET] Failed to start UDP packet");
        return false;
    }

    // Buffer the data
    uint16_t buffered = Ethernet.socketBufferData(self->sockindex, 0, msg, msgSize);
    if (buffered != msgSize) {
        if (DEBUG_SOCKET)
            LOG_DEBUG("[W5500_SOCKET] Failed to buffer all UDP data");
        return false;
    }

    // Send the packet
    bool result = Ethernet.socketSendUDP(self->sockindex);

    if (DEBUG_SOCKET) {
        if (result) {
            LOG_DEBUG("[W5500_SOCKET] UDP packet sent to %s:%d", address, port);
        } else {
            LOG_DEBUG("[W5500_SOCKET] Failed to send UDP packet");
        }
    }

    return result;
}

int
UdpSocket_receiveFrom(UdpSocket self, char* address, int maxAddrSize, uint8_t* msg, int msgSize)
{
    if (self == NULL || self->sockindex >= MAX_SOCK_NUM || msg == NULL || msgSize <= 0) {
        return -1;
    }

    // Check for available data
    uint16_t available = Ethernet.socketRecvAvailable(self->sockindex);
    if (available == 0) {
        return 0; // No data available
    }

    // Receive data
    int bytesReceived = Ethernet.socketRecv(self->sockindex, msg, msgSize);

    if (bytesReceived > 0 && address != NULL && maxAddrSize > 0) {
        // W5500 doesn't provide sender address info directly
        // Set to unknown for now
        strncpy(address, "unknown:0", maxAddrSize - 1);
        address[maxAddrSize - 1] = '\0';
    }

    if (DEBUG_SOCKET && bytesReceived > 0) {
        LOG_DEBUG("[W5500_SOCKET] Received %d bytes via UDP", bytesReceived);
    }

    return bytesReceived;
}

void
UdpSocket_destroy(UdpSocket self)
{
    if (self != NULL) {
        if (self->sockindex < MAX_SOCK_NUM) {
            Ethernet.socketClose(self->sockindex);
            socketUsageTable[self->sockindex] = SOCKET_UNUSED; // Mark as unused
            socketCount--;
        }
        GLOBAL_FREEMEM(self);
    }
}
