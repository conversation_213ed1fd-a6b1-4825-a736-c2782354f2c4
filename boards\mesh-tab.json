{"build": {"arduino": {"ldscript": "esp32s3_out.ld", "partitions": "default_16MB.csv", "memory_type": "qio_qspi"}, "core": "esp32", "extra_flags": ["-DBOARD_HAS_PSRAM", "-DARDUINO_USB_CDC_ON_BOOT=1", "-DARDUINO_USB_MODE=0", "-DARDUINO_RUNNING_CORE=1", "-DARDUINO_EVENT_RUNNING_CORE=1"], "f_cpu": "240000000L", "f_flash": "80000000L", "flash_mode": "qio", "hwids": [["0x303A", "0x80D6"]], "mcu": "esp32s3", "variant": "mesh-tab"}, "connectivity": ["wifi", "bluetooth", "lora"], "debug": {"default_tool": "esp-builtin", "onboard_tools": ["esp-builtin"], "openocd_target": "esp32s3.cfg"}, "frameworks": ["a<PERSON><PERSON><PERSON>", "espidf"], "name": "ESP32-S3 WROOM-1 N16R2 (16 MB FLASH, 2 MB PSRAM)", "upload": {"flash_size": "16MB", "maximum_ram_size": 327680, "maximum_size": 16777216, "use_1200bps_touch": true, "wait_for_upload_port": true, "require_upload_port": true, "speed": 460800}, "url": "https://github.com/valzzu/Mesh-Tab", "vendor": "E<PERSON>ress<PERSON>"}