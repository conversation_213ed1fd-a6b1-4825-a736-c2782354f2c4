{"build": {"arduino": {"ldscript": "esp32s3_out.ld", "memory_type": "qio_opi", "partitions": "default_8MB.csv"}, "core": "esp32", "extra_flags": ["-DBOARD_HAS_PSRAM", "-DUNPHONE_SPIN=9", "-DARDUINO_USB_CDC_ON_BOOT=1", "-DARDUINO_USB_MODE=0", "-DARDUINO_RUNNING_CORE=1", "-DARDUINO_EVENT_RUNNING_CORE=1"], "f_cpu": "240000000L", "f_flash": "80000000L", "flash_mode": "qio", "hwids": [["0x16D0", "0x1178"], ["0x303a", "0x1001"]], "mcu": "esp32s3", "variant": "unphone"}, "connectivity": ["wifi", "bluetooth", "lora"], "debug": {"default_tool": "esp-builtin", "onboard_tools": ["esp-builtin"], "openocd_target": "esp32s3.cfg"}, "frameworks": ["a<PERSON><PERSON><PERSON>", "espidf"], "name": "unPhone", "upload": {"flash_size": "8MB", "maximum_ram_size": 327680, "maximum_size": 8323072, "use_1200bps_touch": true, "wait_for_upload_port": true, "require_upload_port": true, "speed": 921600}, "url": "https://unphone.net/", "vendor": "University of Sheffield"}