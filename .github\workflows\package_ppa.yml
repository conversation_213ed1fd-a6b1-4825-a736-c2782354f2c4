name: Package for Launchpad PPA

on:
  workflow_call:
    secrets:
      PPA_GPG_PRIVATE_KEY:
        required: true
    inputs:
      ppa_repo:
        description: Meshtastic PPA to target
        required: true
        type: string
      series:
        description: Ubuntu series to target
        required: true
        type: string

permissions:
  contents: write
  packages: write

jobs:
  build-debian-src:
    uses: ./.github/workflows/build_debian_src.yml
    secrets: inherit
    with:
      series: ${{ inputs.series }}
      build_location: ppa

  package-ppa:
    runs-on: ubuntu-24.04
    needs: build-debian-src
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          submodules: recursive
          path: meshtasticd
          ref: ${{github.event.pull_request.head.ref}}
          repository: ${{github.event.pull_request.head.repo.full_name}}

      - name: Install deps
        shell: bash
        run: |
          sudo apt-get update -y --fix-missing
          sudo apt-get install -y dput

      - name: Import GPG key
        uses: crazy-max/ghaction-import-gpg@v6
        with:
          gpg_private_key: ${{ secrets.PPA_GPG_PRIVATE_KEY }}
        id: gpg

      - name: Get release version string
        working-directory: meshtasticd
        run: |
          echo "deb=$(./bin/buildinfo.py deb)" >> $GITHUB_OUTPUT
        env:
          BUILD_LOCATION: ppa
        id: version

      - name: Download artifacts
        uses: actions/download-artifact@v4
        with:
          name: firmware-debian-${{ steps.version.outputs.deb }}~${{ inputs.series }}-src
          merge-multiple: true

      - name: Display structure of downloaded files
        run: ls -lah

      - name: Publish with dput
        if: ${{ github.event_name != 'pull_request_target' && github.event_name != 'pull_request' }}
        run: |
          dput ${{ inputs.ppa_repo }} meshtasticd_${{ steps.version.outputs.deb }}~${{ inputs.series }}_source.changes
