#include "ota_update.h"
#include "configuration.h"
#include <Arduino.h>
#include <SPI.h>
#include <Ethernet.h>
#include <Update.h>
#include <ArduinoOTA.h>
#include <esp_partition.h>
#include <esp_app_format.h>
#include <esp_ota_ops.h>

// Static constants definition
const char* ota_update::OTA_SERVER = "th.ota.roulink.com";
const char* ota_update::OTA_PATH = "/download/Sec_gate_firmware_";
const char* ota_update::firmware_version = "v1";

ota_update *OTA_Update = nullptr;

// Constructor
ota_update::ota_update(const char *name) : OSThread(name)
{
    // Initialize member variables
    task_interval = 30000; // 30 seconds
    last_ota_check = 0;
    otaInProgress = false;
    otaProgress = 0;
    otaTotal = 0;
    currentOffset = 0;
    totalSize = 0;
    partSize = 0;
    firstChunk = true;
    init();
}

// Initialize the OTA thread
bool ota_update::init()
{
    LOG_INFO("Initializing OTA Update Thread");
    
    // Check PSRAM status
    LOG_INFO("PSRAM found: %s", psramFound() ? "YES" : "NO");
    if (psramFound()) {
        LOG_INFO("PSRAM size: %d bytes", ESP.getPsramSize());
        LOG_INFO("Free PSRAM: %d bytes", ESP.getFreePsram());
    }
    
    // Print current app information
    printCurrentAppInfo();
    
    // // Initialize Ethernet
    // if (!initializeEthernet()) {
    //     LOG_ERROR("Failed to initialize Ethernet");
    //     return false;
    // }
    
    // // Start Ethernet connection
    // if (!startEthernetConnection()) {
    //     handleEthernetError();
    //     return false;
    // }
    
    // Print network information
    // printNetworkInfo();
    
    LOG_INFO("OTA Update Thread initialized successfully");
    return true;
}

// Main thread execution
int32_t ota_update::runOnce()
{
    // Check Ethernet connection status
    if (Ethernet.linkStatus() == LinkOFF) {
        LOG_WARN("Ethernet connection disconnected");
        return task_interval;
    }
    
    // Check if it's time for OTA check
    uint32_t now = millis();
    if (now - last_ota_check >= task_interval) {
        last_ota_check = now;
        
        LOG_INFO("Performing periodic OTA check");
        
        // Check if firmware file exists and download if available
        if (performOtaUpdate()) {
            // If OTA was successful, the device will restart
            // This code should not be reached
            LOG_INFO("OTA completed, device should restart...");
        } else {
            LOG_DEBUG("No OTA update available, continuing normal operation");
        }
    }
    
    // Maintain Ethernet connection
    Ethernet.maintain();
    
    return task_interval;
}

// Initialize Ethernet hardware
bool ota_update::initializeEthernet()
{
    // pinMode(PIN_ETHERNET_RESET, OUTPUT);
    // digitalWrite(PIN_ETHERNET_RESET, LOW);
    // delay(100);
    // digitalWrite(PIN_ETHERNET_RESET, HIGH);

    // Ethernet.init(PIN_SPI_SS);
    // SPI.begin(ETH_SCK, ETH_MISO, ETH_MOSI, ETH_CS);
    // LOG_DEBUG("SPI.begin(SCK=%d, MISO=%d, MOSI=%d, NSS=%d)", ETH_SCK, ETH_MISO, ETH_MOSI, ETH_CS);
    // SPI.setFrequency(8000000); // 8MHz SPI frequency
    
    return true;
}

// Start Ethernet connection with DHCP
bool ota_update::startEthernetConnection()
{
    uint8_t mac[6];
    assert(esp_efuse_mac_get_default(mac) == ESP_OK);
    mac[0] &= 0xfe;

    LOG_INFO("Starting Ethernet DHCP");
    int status = Ethernet.begin(mac);

    return (status != 0);
}

// Handle Ethernet connection errors
void ota_update::handleEthernetError()
{
    if (Ethernet.hardwareStatus() == EthernetNoHardware) {
        LOG_ERROR("Ethernet shield was not found");
    } else if (Ethernet.linkStatus() == LinkOFF) {
        LOG_ERROR("Ethernet cable is not connected");
    } else {
        LOG_ERROR("Unknown Ethernet error");
    }
}

// Print network information
void ota_update::printNetworkInfo()
{
    LOG_INFO("Local IP %u.%u.%u.%u", Ethernet.localIP()[0], Ethernet.localIP()[1], 
             Ethernet.localIP()[2], Ethernet.localIP()[3]);
    LOG_INFO("Subnet Mask %u.%u.%u.%u", Ethernet.subnetMask()[0], Ethernet.subnetMask()[1], 
             Ethernet.subnetMask()[2], Ethernet.subnetMask()[3]);
    LOG_INFO("Gateway IP %u.%u.%u.%u", Ethernet.gatewayIP()[0], Ethernet.gatewayIP()[1], 
             Ethernet.gatewayIP()[2], Ethernet.gatewayIP()[3]);
    LOG_INFO("DNS Server IP %u.%u.%u.%u", Ethernet.dnsServerIP()[0], Ethernet.dnsServerIP()[1], 
             Ethernet.dnsServerIP()[2], Ethernet.dnsServerIP()[3]);
    LOG_INFO("Ethernet connection successful!");
}

// Print current app partition information
void ota_update::printCurrentPartitionInfo()
{
    // Get current running partition
    const esp_partition_t* running_partition = esp_ota_get_running_partition();
    if (running_partition != NULL) {
        LOG_INFO("Partition Label: %s", running_partition->label);
        LOG_INFO("Partition Type: %d (%s)", running_partition->type,
                 (running_partition->type == ESP_PARTITION_TYPE_APP) ? "APP" : 
                 (running_partition->type == ESP_PARTITION_TYPE_DATA) ? "DATA" : "UNKNOWN");
        
        const char* subtype_str;
        switch(running_partition->subtype) {
            case ESP_PARTITION_SUBTYPE_APP_FACTORY: subtype_str = "FACTORY"; break;
            case ESP_PARTITION_SUBTYPE_APP_OTA_0: subtype_str = "OTA_0"; break;
            case ESP_PARTITION_SUBTYPE_APP_OTA_1: subtype_str = "OTA_1"; break;
            case ESP_PARTITION_SUBTYPE_APP_OTA_2: subtype_str = "OTA_2"; break;
            case ESP_PARTITION_SUBTYPE_APP_OTA_3: subtype_str = "OTA_3"; break;
            default: subtype_str = "UNKNOWN"; break;
        }
        LOG_INFO("Partition Subtype: %d (%s)", running_partition->subtype, subtype_str);
        
        LOG_INFO("Partition Address: 0x%08X", running_partition->address);
        LOG_INFO("Partition Size: %d bytes (%.2f KB)", running_partition->size, 
                 running_partition->size / 1024.0);
        LOG_INFO("Partition Encrypted: %s", running_partition->encrypted ? "YES" : "NO");
    } else {
        LOG_ERROR("Failed to get running partition info");
    }
}

// Print current app descriptor information
void ota_update::printCurrentAppDescriptor()
{
    LOG_INFO("=== Current App Descriptor Information ===");

    // Get current running partition
    const esp_partition_t* running_partition = esp_ota_get_running_partition();
    if (running_partition == NULL) {
        LOG_ERROR("Failed to get running partition");
        return;
    }

    // Read app descriptor from partition
    esp_app_desc_t app_desc;
    esp_err_t err = esp_ota_get_partition_description(running_partition, &app_desc);

    if (err == ESP_OK) {
        LOG_INFO("App Magic Word: 0x%08X", app_desc.magic_word);
        LOG_INFO("App Secure Version: %d", app_desc.secure_version);
        LOG_INFO("Version: %s", app_desc.version);
        LOG_INFO("Project Name: %s", app_desc.project_name);
        LOG_INFO("Compile Time: %s", app_desc.time);
        LOG_INFO("Compile Date: %s", app_desc.date);
        LOG_INFO("IDF Version: %s", app_desc.idf_ver);

        // Print SHA256 hash
        char sha_str[65];
        for (int i = 0; i < 32; i++) {
            sprintf(sha_str + i*2, "%02x", app_desc.app_elf_sha256[i]);
        }
        LOG_INFO("App SHA256: %s", sha_str);
    } else {
        LOG_ERROR("Failed to get app descriptor: %s", esp_err_to_name(err));
    }
}

// Print complete current app information
void ota_update::printCurrentAppInfo()
{
    LOG_INFO("CURRENT APPLICATION INFORMATION");
    printCurrentPartitionInfo();
    printCurrentAppDescriptor();
    LOG_INFO("============================================================");
}

// Connect to OTA server
bool ota_update::connectToServer(EthernetClient& client)
{
    int ret = client.connect(OTA_SERVER, OTA_PORT);
    if (!ret) {
        LOG_ERROR("Unable to connect to %s:%d", OTA_SERVER, OTA_PORT);
        return false;
    }
    LOG_INFO("Connected to %s:%d", OTA_SERVER, OTA_PORT);
    return true;
}

// Send HTTP Range request
void ota_update::sendHttpRequest(EthernetClient& client)
{
    LOG_DEBUG("Sending HTTP request for bytes %d-%d", currentOffset, currentOffset + CHUNK_SIZE - 1);
    client.print("GET ");
    client.print(OTA_PATH);
    client.print(firmware_version);
    client.print(".bin");
    client.println(" HTTP/1.1");
    client.print("Host: ");
    client.println(OTA_SERVER);
    client.printf("Range: bytes=%d-%d\r\n", currentOffset, currentOffset + CHUNK_SIZE - 1);
    client.println("Connection: close");
    client.println();
    LOG_DEBUG("HTTP request sent, waiting for response...");
    delay(50); // Wait for request to be sent
}

// Parse HTTP response headers
bool ota_update::parseHttpHeaders(EthernetClient& client)
{
    unsigned long timeout = millis() + 15000; // 15 second timeout
    bool headersReceived = false;
    unsigned long lastCheck = millis();

    LOG_DEBUG("Waiting for response, timeout in %d ms", 15000);

    while (client.connected() && millis() < timeout) {
        // Print status every 10 seconds
        if (millis() - lastCheck > 10000) {
            LOG_DEBUG("Still waiting... connected: %d, available: %d, time left: %d ms",
                     client.connected(), client.available(), timeout - millis());
            lastCheck = millis();
        }

        if (client.available()) {
            String line = client.readStringUntil('\n');
            line.trim();
            LOG_DEBUG("Header: %s", line.c_str());

            // Parse Content-Range header
            if (line.startsWith("Content-Range:")) {
                int dashPos = line.lastIndexOf('-');
                int slashPos = line.lastIndexOf('/');
                if (dashPos > 0 && slashPos > dashPos) {
                    partSize = line.substring(dashPos + 1, slashPos).toInt();
                    LOG_DEBUG("Current chunk end: %d bytes", partSize);
                }
                if (slashPos > 0 && firstChunk) {
                    totalSize = line.substring(slashPos + 1).toInt();
                    LOG_INFO("Total file size: %d bytes", totalSize);
                    firstChunk = false;
                }
            }

            if (line.length() == 0) {
                LOG_DEBUG("--- HTTP headers end ---");
                headersReceived = true;
                break;
            }
        }
        delay(2);
    }

    if (!headersReceived) {
        LOG_ERROR("Timeout waiting for HTTP headers. Connected: %d, Available: %d",
                 client.connected(), client.available());
        return false;
    }
    return true;
}

// Read chunk data from server
bool ota_update::readChunkData(EthernetClient& client, uint8_t* buffer, size_t& bytesRead)
{
    bytesRead = 0;
    LOG_DEBUG("Reading chunk data...");

    unsigned long dataTimeout = millis() + 60000; // 30 second data read timeout

    while (bytesRead < CHUNK_SIZE && client.connected() && millis() < dataTimeout) {
        if (client.available()) {
            size_t available = client.available();
            size_t toRead = min(available, CHUNK_SIZE - bytesRead);

            size_t actualRead = client.read(buffer + bytesRead, toRead);
            bytesRead += actualRead;

            // Reset timeout since we received data
            dataTimeout = millis() + 10000;
        }
        delay(1);
    }

    if (millis() >= dataTimeout) {
        LOG_ERROR("Timeout reading chunk data");
        return false;
    }

    LOG_DEBUG("Chunk reading complete: %d bytes", bytesRead);
    return true;
}

// Write chunk data to Update
bool ota_update::writeChunkData(uint8_t* buffer, size_t bytesRead)
{
    size_t written = Update.write(buffer, bytesRead);

    if (written > 0) {
        if(written != bytesRead){
            LOG_ERROR("Write size mismatch! %u != %u", written, bytesRead);
            return false;
        }

        currentOffset += bytesRead;
        LOG_INFO("Successfully wrote %d bytes, total progress: %d/%d (%.1f%%)",
                 bytesRead, currentOffset, totalSize,
                 (float)currentOffset / totalSize * 100.0);
        return true;
    } else {
        LOG_ERROR("Write ERROR: %s", Update.errorString());
        return false;
    }
}

// Download a single chunk of firmware
bool ota_update::downloadChunk()
{
    LOG_DEBUG("Downloading chunk at offset %d...", currentOffset);

    EthernetClient client;

    // Step 1: Connect to server
    if (!connectToServer(client)) {
        return false;
    }

    // Step 2: Send HTTP request
    sendHttpRequest(client);

    // Step 3: Parse HTTP headers
    if (!parseHttpHeaders(client)) {
        client.stop();
        return false;
    }

    // Step 4: Allocate buffer
    uint8_t* buffer = (uint8_t*)ps_malloc(CHUNK_SIZE);
    if (buffer == NULL) {
        LOG_ERROR("PSRAM allocation failed!");
        client.stop();
        return false;
    }

    // Step 5: Read chunk data
    size_t bytesRead = 0;
    if (!readChunkData(client, buffer, bytesRead)) {
        free(buffer);
        client.stop();
        return false;
    }

    // Step 6: Write chunk data
    bool writeSuccess = writeChunkData(buffer, bytesRead);

    // Cleanup
    free(buffer);
    client.stop();

    if (writeSuccess) {
        LOG_DEBUG("Chunk download completed");
        return true;
    } else {
        return false;
    }
}

// Check if firmware file exists on server
bool ota_update::checkFirmwareExists()
{
    LOG_INFO("Checking if firmware file exists on server...");

    EthernetClient client;

    // Connect to server
    if (!connectToServer(client)) {
        return false;
    }

    // Send HTTP HEAD request to check file existence
    LOG_DEBUG("Sending HEAD request to check file: %s%s%s", OTA_PATH,firmware_version,".bin");
    client.print("HEAD ");
    client.print(OTA_PATH);
    client.print(firmware_version);
    client.print(".bin");
    client.println(" HTTP/1.1");
    client.print("Host: ");
    client.println(OTA_SERVER);
    client.println("Connection: close");
    client.println();

    delay(50);

    // Parse response headers
    unsigned long timeout = millis() + 10000; // 10 second timeout
    bool headersReceived = false;
    bool fileExists = false;

    while (client.connected() && millis() < timeout) {
        if (client.available()) {
            String line = client.readStringUntil('\n');
            line.trim();
            LOG_DEBUG("Check Header: %s", line.c_str());

            // Check HTTP status code in first line
            if (line.startsWith("HTTP/")) {
                if (line.indexOf("200") != -1) {
                    LOG_INFO("Firmware file exists on server");
                    fileExists = true;
                } else if (line.indexOf("404") != -1) {
                    LOG_INFO("Firmware file not found on server (404)");
                    fileExists = false;
                } else {
                    LOG_WARN("Unexpected HTTP status: %s", line.c_str());
                    fileExists = false;
                }
                headersReceived = true;
                break;
            }
        }
        delay(2);
    }

    client.stop();

    if (!headersReceived) {
        LOG_ERROR("Timeout waiting for server response");
        return false;
    }

    return fileExists;
}

// Download complete firmware from server
bool ota_update::downloadFirmware()
{
    LOG_INFO("Starting firmware download...");

    // Reset download state
    currentOffset = 0;
    totalSize = 0;
    firstChunk = true;

    // Begin Update process
    if (!Update.begin(UPDATE_SIZE_UNKNOWN)) {
        LOG_ERROR("Update.begin() failed: %s", Update.errorString());
        return false;
    }

    // Loop to download all data chunks
    int retryCount = 0;
    const int maxRetries = 100;

    while (true) {
        if (!downloadChunk()) {
            retryCount++;
            LOG_WARN("Failed to download chunk (attempt %d/%d)", retryCount, maxRetries);

            if (retryCount >= maxRetries) {
                LOG_ERROR("Max retries reached, aborting...");
                Update.abort();
                return false;
            }

            // Wait longer before retry
            LOG_DEBUG("Waiting 1 second before retry...");
            delay(1000);
            continue;
        }

        // Reset retry counter
        retryCount = 0;

        // Check if download is complete
        if (currentOffset >= totalSize) {
            LOG_INFO("All chunks downloaded successfully!");
            break;
        }

        // Delay to avoid server pressure
        LOG_DEBUG("Progress: %d/%d bytes (%.1f%%), waiting 200ms before next chunk...",
                 currentOffset, totalSize, (float)currentOffset / totalSize * 100.0);
        delay(200);
    }

    // Complete Update process
    if (Update.end(true)) {
        LOG_INFO("Update completed successfully!");
        LOG_INFO("Restarting in 3 seconds...");
        delay(3000);
        ESP.restart();
        return true;
    } else {
        LOG_ERROR("Update.end() failed: %s", Update.errorString());
        return false;
    }
}

// Main OTA process - check and download if file exists
bool ota_update::performOtaUpdate()
{
    LOG_INFO("=== Starting OTA Update Process ===");

    // Step 1: Check if firmware file exists
    if (!checkFirmwareExists()) {
        LOG_DEBUG("OTA cancelled - firmware file not available");
        return false;
    }

    // Step 2: Download firmware
    LOG_INFO("Firmware file found, starting download...");
    return downloadFirmware();
}
