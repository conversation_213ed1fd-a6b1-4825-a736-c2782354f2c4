name: Build STM32

on:
  workflow_call:
    inputs:
      board:
        required: true
        type: string

permissions: read-all

jobs:
  build-stm32:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4

      - name: Build STM32WL
        id: build
        uses: ./.github/actions/build-variant
        with:
          github_token: ${{ secrets.GITHUB_TOKEN }}
          board: ${{ inputs.board }}
          build-script-path: bin/build-stm32.sh
          artifact-paths: |
            release/*.hex
            release/*.bin
            release/*.elf
          arch: stm32
