---
name: Semgrep Full Scan

on:
  workflow_dispatch:
  schedule:
    - cron: 0 1 * * 6

permissions:
  actions: read
  contents: read
  security-events: write

jobs:
  semgrep-full:
    runs-on: ubuntu-22.04
    container:
      image: semgrep/semgrep

    steps:
      # step 1
      - name: clone application source code
        uses: actions/checkout@v4

      # step 2
      - name: full scan
        run: |
          semgrep \
            --sarif --output report.sarif \
            --metrics=off \
            --config="p/default"

      # step 3
      - name: save report as pipeline artifact
        uses: actions/upload-artifact@v4
        with:
          name: report.sarif
          overwrite: true
          path: report.sarif

      # step 4
      - name: publish code scanning alerts
        uses: github/codeql-action/upload-sarif@v3
        with:
          sarif_file: report.sarif
          category: semgrep
