[stm32_base]
extends = arduino_base
platform = ststm32
platform_packages = platformio/framework-arduinoststm32@https://github.com/stm32duino/Arduino_Core_STM32/archive/2.10.1.zip
extra_scripts =
  ${env.extra_scripts}
  post:extra_scripts/extra_stm32.py

build_type = release

build_flags =
  ${arduino_base.build_flags}
  -flto
  -Isrc/platform/stm32wl -g
  -DMESHTASTIC_EXCLUDE_ENVIRONMENTAL_SENSOR
  -DMESHTASTIC_EXCLUDE_INPUTBROKER
  -DMESHTASTIC_EXCLUDE_I2C
  -DMESHTASTIC_EXCLUDE_POWERMON
  -DMESHTASTIC_EXCLUDE_SCREEN
  -DMESHTASTIC_EXCLUDE_MQTT
  -DMESHTASTIC_EXCLUDE_BLUETOOTH
  -DMESHTASTIC_EXCLUDE_GPS
  ;-DDEBUG_MUTE
  -fmerge-all-constants
  -ffunction-sections
  -fdata-sections

build_src_filter =
  ${arduino_base.build_src_filter} -<platform/esp32/> -<nimble/> -<mesh/api/> -<mesh/wifi/> -<mesh/http/> -<modules/esp32> -<mesh/eth/> -<input> -<buzz> -<modules/RemoteHardwareModule.cpp> -<platform/nrf52> -<platform/portduino> -<platform/rp2xx0> -<mesh/raspihttp>

board_upload.offset_address = 0x08000000
upload_protocol = stlink
debug_tool = stlink

lib_deps =
  ${env.lib_deps}
  ${radiolib_base.lib_deps}
  https://github.com/caveman99/Crypto/archive/eae9c768054118a9399690f8af202853d1ae8516.zip

lib_ignore =
  mathertel/OneButton@2.6.1
  Wire
