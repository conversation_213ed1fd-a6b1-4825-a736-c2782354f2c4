{"build": {"arduino": {"ldscript": "esp32s3_out.ld", "partitions": "default_8MB.csv", "memory_type": "qio_opi"}, "core": "esp32", "extra_flags": ["-DBOARD_HAS_PSRAM", "-DARDUINO_USB_CDC_ON_BOOT=0", "-DARDUINO_USB_MODE=1", "-DARDUINO_RUNNING_CORE=1", "-DARDUINO_EVENT_RUNNING_CORE=1"], "f_cpu": "240000000L", "f_flash": "80000000L", "f_boot": "120000000L", "boot": "qio", "flash_mode": "qio", "hwids": [["0x1A86", "0x7523"]], "mcu": "esp32s3", "variant": "esp32s3"}, "connectivity": ["wifi", "bluetooth", "lora"], "debug": {"default_tool": "esp-builtin", "onboard_tools": ["esp-builtin"], "openocd_target": "esp32s3.cfg"}, "frameworks": ["a<PERSON><PERSON><PERSON>"], "name": "Seeed Studio SenseCAP Indicator", "upload": {"flash_size": "8MB", "maximum_ram_size": 327680, "maximum_size": 8388608, "require_upload_port": false, "use_1200bps_touch": true, "wait_for_upload_port": false, "speed": 921600}, "url": "https://www.seeedstudio.com/Indicator-for-Meshtastic.html", "vendor": "Seeed Studio"}