[nrf52_base]
; Instead of the standard nordicnrf52 platform, we use our fork which has our added variant files
platform = platformio/nordicnrf52@^10.8.0
extends = arduino_base
platform_packages =
  ; our custom Git version until they merge our PR
  platformio/framework-arduinoadafruitnrf52 @ https://github.com/meshtastic/Adafruit_nRF52_Arduino#e13f5820002a4fb2a5e6754b42ace185277e5adf
  platformio/toolchain-gccarmnoneeabi@~1.90301.0

build_type = debug
build_flags =
  -include arch/nrf52/cpp_overrides/lfs_util.h
  ${arduino_base.build_flags}
  -DSERIAL_BUFFER_SIZE=1024
  -Wno-unused-variable
  -Isrc/platform/nrf52
  -DLFS_NO_ASSERT                      ; Disable LFS assertions , see https://github.com/meshtastic/firmware/pull/3818
  -DMESHTASTIC_EXCLUDE_AUDIO=1
  -DMESHTASTIC_EXCLUDE_PAXCOUNTER=1

build_src_filter = 
  ${arduino_base.build_src_filter} -<platform/esp32/> -<platform/stm32wl> -<nimble/> -<mesh/wifi/> -<mesh/api/> -<mesh/http/> -<modules/esp32> -<platform/rp2xx0> -<mesh/eth/> -<mesh/raspihttp>

lib_deps=
  ${arduino_base.lib_deps}
  ${radiolib_base.lib_deps}
  rweather/Crypto@^0.4.0

lib_ignore =
  BluetoothOTA
  lvgl