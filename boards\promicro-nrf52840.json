{"build": {"arduino": {"ldscript": "nrf52840_s140_v6.ld"}, "core": "nRF5", "cpu": "cortex-m4", "extra_flags": "-DARDUINO_NRF52840_FEATHER -DNRF52840_XXAA", "f_cpu": "64000000L", "hwids": [["0x239A", "0x00B3"], ["0x239A", "0x8029"], ["0x239A", "0x0029"], ["0x239A", "0x002A"], ["0x239A", "0x802A"]], "usb_product": "ProMicro compatible nRF52840", "mcu": "nrf52840", "variant": "promicro_diy", "bsp": {"name": "adafruit"}, "softdevice": {"sd_flags": "-DS140", "sd_name": "s140", "sd_version": "6.1.1", "sd_fwid": "0x00B6"}, "bootloader": {"settings_addr": "0xFF000"}}, "connectivity": ["bluetooth"], "debug": {"jlink_device": "nRF52840_xxAA", "svd_path": "nrf52840.svd"}, "frameworks": ["a<PERSON><PERSON><PERSON>"], "name": "ProMicro compatible nRF52840", "upload": {"maximum_ram_size": 248832, "maximum_size": 815104, "speed": 115200, "protocol": "nrfutil", "protocols": ["nrfutil", "jlink", "nrfjprog", "stlink"], "use_1200bps_touch": true, "require_upload_port": true, "wait_for_upload_port": true}, "url": "https://www.nologo.tech/product/otherboard/NRF52840.html", "vendor": "Nologo"}