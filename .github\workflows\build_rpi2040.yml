name: Build RPI2040

on:
  workflow_call:
    inputs:
      board:
        required: true
        type: string

permissions: read-all

jobs:
  build-rpi2040:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4

      - name: Build Raspberry Pi 2040
        id: build
        uses: ./.github/actions/build-variant
        with:
          github_token: ${{ secrets.GITHUB_TOKEN }}
          board: ${{ inputs.board }}
          build-script-path: bin/build-rpi2040.sh
          artifact-paths: |
            release/*.uf2
            release/*.elf
          arch: rp2040
