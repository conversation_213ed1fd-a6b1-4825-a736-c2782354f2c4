{"build": {"arduino": {"ldscript": "esp32s3_out.ld", "memory_type": "qio_opi"}, "core": "esp32", "extra_flags": ["-DBOARD_HAS_PSRAM", "-DARDUINO_USB_CDC_ON_BOOT=1", "-DARDUINO_USB_MODE=0", "-DARDUINO_RUNNING_CORE=1", "-DARDUINO_EVENT_RUNNING_CORE=0"], "f_cpu": "240000000L", "f_flash": "80000000L", "flash_mode": "qio", "hwids": [["0x2886", "0x0059"]], "mcu": "esp32s3", "variant": "icarus"}, "connectivity": ["wifi", "bluetooth", "lora"], "debug": {"default_tool": "esp-builtin", "onboard_tools": ["esp-builtin"], "openocd_target": "esp32s3.cfg"}, "frameworks": ["a<PERSON><PERSON><PERSON>", "espidf"], "name": "icarus", "upload": {"flash_size": "8MB", "maximum_ram_size": 8388608, "maximum_size": 8388608, "use_1200bps_touch": true, "wait_for_upload_port": true, "require_upload_port": true, "speed": 921600}, "url": "https://icarus.azlan.works", "vendor": "<PERSON>"}