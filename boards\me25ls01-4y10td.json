{"build": {"arduino": {"ldscript": "nrf52840_s140_v7.ld"}, "core": "nRF5", "cpu": "cortex-m4", "extra_flags": "-DME25LS01_4Y10TD -DNRF52840_XXAA", "f_cpu": "64000000L", "hwids": [["0x239A", "0x8029"], ["0x239A", "0x0029"], ["0x239A", "0x002A"], ["0x239A", "0x802A"]], "usb_product": "ME25LS01-BOOT", "mcu": "nrf52840", "variant": "MINEWSEMI_ME25LS01", "bsp": {"name": "adafruit"}, "softdevice": {"sd_flags": "-DS140", "sd_name": "s140", "sd_version": "7.3.0", "sd_fwid": "0x0123"}, "bootloader": {"settings_addr": "0xFF000"}}, "connectivity": ["bluetooth"], "debug": {"jlink_device": "nRF52840_xxAA", "svd_path": "nrf52840.svd"}, "frameworks": ["a<PERSON><PERSON><PERSON>"], "name": "Minesemi ME25LS01", "upload": {"maximum_ram_size": 248832, "maximum_size": 815104, "speed": 115200, "protocol": "nrfutil", "protocols": ["jlink", "nrfjprog", "nrfutil", "stlink", "cmsis-dap", "blackmagic"], "use_1200bps_touch": true, "require_upload_port": true, "wait_for_upload_port": true}, "url": "https://en.minewsemi.com/lora-module/lr1110-nrf52840-me25LS01l", "vendor": "<PERSON><PERSON><PERSON>"}