{"build": {"arduino": {"ldscript": "esp32s3_out.ld", "partitions": "default_8MB.csv"}, "core": "esp32", "extra_flags": ["-DHELTEC_WIRELESS_TRACKER", "-DARDUINO_USB_CDC_ON_BOOT=1", "-DARDUINO_USB_MODE=0", "-DARDUINO_RUNNING_CORE=1", "-DARDUINO_EVENT_RUNNING_CORE=1"], "f_cpu": "240000000L", "f_flash": "80000000L", "flash_mode": "qio", "hwids": [["0x303A", "0x1001"]], "mcu": "esp32s3", "variant": "heltec_wireless_tracker"}, "connectivity": ["wifi", "bluetooth", "lora"], "debug": {"openocd_target": "esp32s3.cfg"}, "frameworks": ["a<PERSON><PERSON><PERSON>", "espidf"], "name": "Heltec Wireless Tracker", "upload": {"flash_size": "8MB", "maximum_ram_size": 327680, "maximum_size": 8388608, "wait_for_upload_port": true, "require_upload_port": true, "speed": 921600}, "url": "https://heltec.org/project/wireless-tracker/", "vendor": "Heltec"}