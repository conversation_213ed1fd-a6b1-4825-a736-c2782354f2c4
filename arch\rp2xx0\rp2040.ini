; Common settings for rp2040 Processor based targets
[rp2040_base]
platform = https://github.com/maxgerhardt/platform-raspberrypi#76ecf3c7e9dd4503af0331154c4ca1cddc4b03e5 ; For arduino-pico >= 4.4.3
extends = arduino_base
platform_packages = framework-arduinopico@https://github.com/earlephilhower/arduino-pico#4.4.3

board_build.core = earlephilhower
board_build.filesystem_size = 0.5m
build_flags = 
  ${arduino_base.build_flags} -Wno-unused-variable -Wcast-align
  -Isrc/platform/rp2xx0
  -Isrc/platform/rp2xx0/hardware_rosc/include
  -Isrc/platform/rp2xx0/pico_sleep/include
  -D__PLAT_RP2040__
#  -D _POSIX_THREADS
build_src_filter = 
  ${arduino_base.build_src_filter} -<platform/esp32/> -<nimble/> -<modules/esp32> -<platform/nrf52/> -<platform/stm32wl> -<mesh/eth/> -<mesh/wifi/> -<mesh/http/> -<mesh/raspihttp>

lib_ignore =
  BluetoothOTA
  lvgl

lib_deps =
  ${arduino_base.lib_deps}
  ${environmental_base.lib_deps}
  ${radiolib_base.lib_deps}
  rweather/Crypto