{"build": {"arduino": {"ldscript": "esp32_out.ld", "partitions": "default_16MB.csv"}, "core": "esp32", "extra_flags": ["-DARDUINO_WIPHONE14", "-DBOARD_HAS_PSRAM", "-mfix-esp32-psram-cache-issue", "-mfix-esp32-psram-cache-strategy=memw"], "f_cpu": "240000000L", "f_flash": "40000000L", "flash_mode": "dio", "mcu": "esp32", "variant": "wiphone", "board": "WiPhone"}, "connectivity": ["wifi", "bluetooth"], "frameworks": ["a<PERSON><PERSON><PERSON>", "espidf"], "name": "WIPhone Integrated 1.4", "upload": {"flash_size": "16MB", "maximum_ram_size": 532480, "maximum_size": 6553600, "maximum_data_size": 4521984, "require_upload_port": true, "speed": 921600}, "url": "https://www.wiphone.io/", "vendor": "HackEDA"}