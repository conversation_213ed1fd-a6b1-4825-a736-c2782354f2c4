; Common settings for ESP targes, mixin with extends = esp32_base
[esp32_base]
extends = arduino_base
custom_esp32_kind = esp32
platform = platformio/espressif32@6.10.0

build_src_filter = 
  ${arduino_base.build_src_filter} -<platform/nrf52/> -<platform/stm32wl> -<platform/rp2xx0> -<mesh/raspihttp>
  -<mesh/raspihttp> +<modules/iec104>
upload_speed = 921600
; debug_init_break = tbreak setup
monitor_filters = esp32_exception_decoder

board_build.filesystem = littlefs

# Remove -DMYNEWT_VAL_BLE_HS_LOG_LVL=LOG_LEVEL_CRITICAL for low level BLE logging.
# See library directory for BLE logging possible values: .pio/libdeps/tbeam/NimBLE-Arduino/src/log_common/log_common.h
# This overrides the BLE logging default of LOG_LEVEL_INFO (1) from: .pio/libdeps/tbeam/NimBLE-Arduino/src/esp_nimble_cfg.h
build_unflags = -fno-lto
build_flags =
  ${arduino_base.build_flags}
  -flto
  -Wall
  -Wextra
  -Isrc/platform/esp32
  -std=c++11
  -DLOG_LOCAL_LEVEL=ESP_LOG_DEBUG
  -DCORE_DEBUG_LEVEL=ARDUHAL_LOG_LEVEL_DEBUG
  -DMYNEWT_VAL_BLE_HS_LOG_LVL=LOG_LEVEL_CRITICAL
  -DAXP_DEBUG_PORT=Serial
  -DCONFIG_BT_NIMBLE_ENABLED
  -DCONFIG_NIMBLE_CPP_LOG_LEVEL=2
  -DCONFIG_BT_NIMBLE_MAX_CCCDS=20
  -DCONFIG_BT_NIMBLE_HOST_TASK_STACK_SIZE=8192
  -DESP_OPENSSL_SUPPRESS_LEGACY_WARNING
  -DSERIAL_BUFFER_SIZE=4096
  -DLIBPAX_ARDUINO
  -DLIBPAX_WIFI
  -DLIBPAX_BLE
  -DHAS_UDP_MULTICAST=1
  ;-DDEBUG_HEAP

lib_deps =
  ${arduino_base.lib_deps}
  ${networking_base.lib_deps}
  ${environmental_base.lib_deps}
  ${radiolib_base.lib_deps}
  https://github.com/meshtastic/esp32_https_server/archive/23665b3adc080a311dcbb586ed5941b5f94d6ea2.zip
  h2zero/NimBLE-Arduino@^1.4.3
  https://github.com/dbinfrago/libpax/archive/3cdc0371c375676a97967547f4065607d4c53fd1.zip
  lewisxhe/XPowersLib@^0.2.7
  https://github.com/meshtastic/ESP32_Codec2/archive/633326c78ac251c059ab3a8c430fcdf25b41672f.zip
  rweather/Crypto@^0.4.0

lib_ignore = 
  segger_rtt
  ESP32 BLE Arduino

; leave this commented out to avoid breaking Windows
;upload_port = /dev/ttyUSB0
;monitor_port = /dev/ttyUSB0

; Please don't delete these lines. JM uses them.
;upload_port = /dev/cu.SLAB_USBtoUART
;monitor_port = /dev/cu.SLAB_USBtoUART

; customize the partition table
; http://docs.platformio.org/en/latest/platforms/espressif32.html#partition-tables
board_build.partitions = partition-table.csv
