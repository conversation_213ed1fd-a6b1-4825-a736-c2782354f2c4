{"build": {"arduino": {"ldscript": "nrf52832_s132_v6.ld"}, "core": "nRF5", "cpu": "cortex-m4", "extra_flags": "-DNRF52832_XXAA -DNRF52", "f_cpu": "64000000L", "hwids": [["0x239A", "0x8029"], ["0x239A", "0x0029"], ["0x239A", "0x002A"], ["0x239A", "0x802A"]], "usb_product": "Feather nRF52832 Express", "mcu": "nrf52832", "variant": "WisCore_RAK4600_Board", "bsp": {"name": "adafruit"}, "softdevice": {"sd_flags": "-DS132", "sd_name": "s132", "sd_version": "6.1.1", "sd_fwid": "0x00B7"}, "zephyr": {"variant": "nrf52_adafruit_feather"}}, "connectivity": ["bluetooth"], "debug": {"jlink_device": "nRF52832_xxAA", "svd_path": "nrf52.svd", "openocd_target": "nrf52840-mdk-rs"}, "frameworks": ["a<PERSON><PERSON><PERSON>", "zephyr"], "name": "Adafruit Bluefruit nRF52832 Feather", "upload": {"maximum_ram_size": 65536, "maximum_size": 524288, "require_upload_port": true, "speed": 115200, "protocol": "nrfutil", "protocols": ["jlink", "nrfjprog", "nrfutil", "stlink"]}, "url": "https://www.adafruit.com/product/3406", "vendor": "Adafruit"}