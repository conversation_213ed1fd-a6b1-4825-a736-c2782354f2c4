{"build": {"arduino": {"ldscript": "esp32s3_out.ld", "partitions": "default_16MB.csv"}, "core": "esp32", "extra_flags": ["-DARDUINO_ESP32S3_DEV", "-DARDUINO_RUNNING_CORE=1", "-DARDUINO_EVENT_RUNNING_CORE=1", "-DARDUINO_USB_CDC_ON_BOOT=1", "-DBOARD_HAS_PSRAM"], "f_cpu": "240000000L", "f_flash": "80000000L", "flash_mode": "qio", "psram_type": "qio", "hwids": [["0x303A", "0x1001"]], "mcu": "esp32s3", "variant": "esp32s3"}, "connectivity": ["wifi", "bluetooth", "lora"], "debug": {"default_tool": "esp-builtin", "onboard_tools": ["esp-builtin"], "openocd_target": "esp32s3.cfg"}, "frameworks": ["a<PERSON><PERSON><PERSON>", "espidf"], "name": "Waveshare ESP32-S3-Pico (16 MB FLASH, 2 MB PSRAM)", "upload": {"flash_size": "16MB", "maximum_ram_size": 327680, "maximum_size": 16777216, "use_1200bps_touch": true, "wait_for_upload_port": true, "require_upload_port": true, "speed": 921600}, "url": "https://www.waveshare.com/esp32-s3-pico.htm", "vendor": "Waveshare"}