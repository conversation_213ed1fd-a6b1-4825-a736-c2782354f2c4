## 🙏 Thank you for sending in a pull request, here's some tips to get started!

### ❌ (Please delete all these tips and replace them with your text) ❌
- Before starting on some new big chunk of code, it it is optional but highly recommended to open an issue first
  to say "Hey, I think this idea X should be implemented and I'm starting work on it. My general plan is Y, any feedback
  is appreciated." This will allow other devs to potentially save you time by not accidentially duplicating work etc...
- Please do not check in files that don't have real changes
- Please do not reformat lines that you didn't have to change the code on
- We recommend using the [Visual Studio Code](https://platformio.org/install/ide?install=vscode) editor along with the ['Trunk Check' extension](https://marketplace.visualstudio.com/items?itemName=trunk.io) (In beta for windows, WSL2 for the linux version),
  because it automatically follows our indentation rules and its auto reformatting will not cause spurious changes to lines.
- If your PR fixes a bug, mention "fixes #bugnum" somewhere in your pull request description.
- If your other co-developers have comments on your PR please tweak as needed.
- Please also enable "Allow edits by maintainers".
- Please do not submit untested code.
- If you do not have the affected hardware to test your code changes adequately against regressions, please indicate this, so that contributors and commnunity members can help test your changes.
- If your PR gets accepted you can request a "Contributor" role in the Meshtastic Discord


## 🤝 Attestations
- [ ] I have tested that my proposed changes behave as described.
- [ ] I have tested that my proposed changes do not cause any obvious regressions on the following devices:
  - [ ] Heltec (Lora32) V3
  - [ ] LilyGo T-Deck 
  - [ ] LilyGo T-Beam
  - [ ] RAK WisBlock 4631
  - [ ] Seeed Studio T-1000E tracker card
  - [ ] Other (please specify below)
