{"build": {"arduino": {"ldscript": "esp32s3_out.ld"}, "core": "esp32", "extra_flags": ["-D ARDUINO_USB_CDC_ON_BOOT=0", "-D ARDUINO_USB_MSC_ON_BOOT=0", "-D ARDUINO_USB_DFU_ON_BOOT=0", "-D ARDUINO_USB_MODE=0", "-D ARDUINO_RUNNING_CORE=1", "-D ARDUINO_EVENT_RUNNING_CORE=1"], "f_cpu": "240000000L", "f_flash": "80000000L", "flash_mode": "qio", "hwids": [["0x303A", "0x1001"]], "mcu": "esp32s3", "variant": "ESP32-S3-WROOM-1-N4"}, "connectivity": ["wifi", "bluetooth"], "debug": {"default_tool": "esp-builtin", "onboard_tools": ["esp-builtin"], "openocd_target": "esp32s3.cfg"}, "frameworks": ["a<PERSON><PERSON><PERSON>", "espidf"], "name": "ESP32-S3-WROOM-1-N4 (4 MB Flash, No PSRAM)", "upload": {"flash_size": "4MB", "maximum_ram_size": 524288, "maximum_size": 4194304, "require_upload_port": true, "speed": 921600}, "url": "https://www.espressif.com/sites/default/files/documentation/esp32-s3-wroom-1_wroom-1u_datasheet_en.pdf", "vendor": "E<PERSON>ress<PERSON>"}