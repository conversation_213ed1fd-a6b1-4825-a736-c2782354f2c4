#pragma once

#include "main.h"
#include "concurrency/OSThread.h"
#include <Arduino.h>

// Forward declarations
class EthernetClient;

// OTA Update Thread Class
class ota_update : public concurrency::OSThread {
public:
    explicit ota_update(const char *name);
    
    bool init();
    virtual int32_t runOnce() override;
    
    // OTA相关配置
    uint32_t task_interval = 30000; // 30秒检查一次
    uint32_t last_ota_check = 0;
    
    // OTA状态
    bool otaInProgress = false;
    size_t otaProgress = 0;
    size_t otaTotal = 0;
    
private:
    // 网络连接相关
    bool initializeEthernet();
    bool startEthernetConnection();
    void handleEthernetError();
    void printNetworkInfo();
    
    // OTA核心功能
    bool checkFirmwareExists();
    bool downloadFirmware();
    bool downloadChunk();
    bool performOtaUpdate();
    
    // HTTP通信相关
    bool connectToServer(EthernetClient& client);
    void sendHttpRequest(EthernetClient& client);
    bool parseHttpHeaders(EthernetClient& client);
    bool readChunkData(EthernetClient& client, uint8_t* buffer, size_t& bytesRead);
    bool writeChunkData(uint8_t* buffer, size_t bytesRead);
    
    // 系统信息相关
    void printCurrentPartitionInfo();
    void printCurrentAppDescriptor();
    void printCurrentAppInfo();
    
    // 下载状态变量
    size_t currentOffset = 0;
    size_t totalSize = 0;
    size_t partSize = 0;
    bool firstChunk = true;
    
    // 常量配置
    static const size_t CHUNK_SIZE = 1024 * 1024; // 1MB per chunk
    static const char* OTA_SERVER;
    static const int OTA_PORT = 80;
    static const char* OTA_PATH;
    static const char* firmware_version;
};

extern ota_update *OTA_Update;

// // Pin definitions for Ethernet (shared with current_read)
// #define ETH_MISO 13
// #define ETH_SCK 12
// #define ETH_MOSI 11
// #define ETH_CS 10
// // #define ETH_RST 15 // 9 gateway
// #define ETH_RST 9 // 15 kaifaban
// #define ETH_INT 14

// #define PIN_SPI_SS ETH_CS
// #define PIN_ETHERNET_RESET ETH_RST

// #define CONFIG_APP_PROJECT_NAME "sec"
