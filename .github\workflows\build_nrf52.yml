name: Build NRF52

on:
  workflow_call:
    inputs:
      board:
        required: true
        type: string

permissions: read-all

jobs:
  build-nrf52:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4

      - name: Build NRF52
        id: build
        uses: ./.github/actions/build-variant
        with:
          github_token: ${{ secrets.GITHUB_TOKEN }}
          board: ${{ inputs.board }}
          build-script-path: bin/build-nrf52.sh
          artifact-paths: |
            release/*.hex
            release/*.uf2
            release/*.elf
            release/*.zip
          arch: nrf52840
