{"build": {"arduino": {"ldscript": "esp32s3_out.ld"}, "core": "esp32", "extra_flags": ["-DLILYGO_T3S3_V1", "-DARDUINO_USB_CDC_ON_BOOT=1", "-DARDUINO_USB_MODE=0", "-DARDUINO_RUNNING_CORE=1", "-DARDUINO_EVENT_RUNNING_CORE=1", "-DBOARD_HAS_PSRAM"], "f_cpu": "240000000L", "f_flash": "80000000L", "flash_mode": "dio", "hwids": [["0x303A", "0x1001"]], "mcu": "esp32s3", "variant": "tlora-t3s3-v1"}, "connectivity": ["wifi", "bluetooth"], "debug": {"openocd_target": "esp32s3.cfg"}, "frameworks": ["a<PERSON><PERSON><PERSON>", "espidf"], "name": "LilyGo TLora-T3S3-V1", "upload": {"flash_size": "4MB", "maximum_ram_size": 327680, "maximum_size": 4194304, "wait_for_upload_port": true, "require_upload_port": true, "speed": 921600}, "url": "http://www.lilygo.cn/", "vendor": "LilyGo"}