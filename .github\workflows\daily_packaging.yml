name: Daily Packaging
on:
  schedule:
    - cron: 0 9 * * *
  workflow_dispatch:
  push:
    branches:
      - master
    paths:
      - debian/**
      - "*.rpkg"
      - .github/workflows/nightly_packaging.yml
      - .github/workflows/build_debian_src.yml
      - .github/workflows/package_ppa.yml
      - .github/workflows/package_obs.yml
      - .github/workflows/hook_copr.yml

permissions:
  contents: write
  packages: write

jobs:
  docker-multiarch:
    uses: ./.github/workflows/docker_manifest.yml
    with:
      release_channel: daily
    secrets: inherit

  package-ppa:
    strategy:
      fail-fast: false
      matrix:
        series: [plucky, oracular, noble, jammy]
    uses: ./.github/workflows/package_ppa.yml
    with:
      ppa_repo: ppa:meshtastic/daily
      series: ${{ matrix.series }}
    secrets: inherit

  package-obs:
    uses: ./.github/workflows/package_obs.yml
    with:
      obs_project: network:Meshtastic:daily
      series: unstable
    secrets: inherit

  hook-copr:
    uses: ./.github/workflows/hook_copr.yml
    with:
      copr_project: daily
    secrets: inherit
