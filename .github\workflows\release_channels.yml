name: Trigger release workflows upon Publish

on:
  release:
    types: [published, released]

permissions:
  contents: write
  packages: write

jobs:
  build-docker:
    uses: ./.github/workflows/docker_manifest.yml
    with:
      release_channel: |-
        ${{ contains(github.event.release.name, 'Beta') && 'beta' || contains(github.event.release.name, 'Alpha') && 'alpha' }}
    secrets: inherit

  package-ppa:
    strategy:
      fail-fast: false
      matrix:
        series: [plucky, oracular, noble, jammy]
    uses: ./.github/workflows/package_ppa.yml
    with:
      ppa_repo: |-
        ppa:meshtastic/${{ contains(github.event.release.name, 'Beta') && 'beta' || contains(github.event.release.name, 'Alpha') && 'alpha' }}
      series: ${{ matrix.series }}
    secrets: inherit

  package-obs:
    uses: ./.github/workflows/package_obs.yml
    with:
      obs_project: |-
        network:Meshtastic:${{ contains(github.event.release.name, 'Beta') && 'beta' || contains(github.event.release.name, 'Alpha') && 'alpha' }}
      series: |-
        ${{ contains(github.event.release.name, 'Beta') && 'beta' || contains(github.event.release.name, 'Alpha') && 'alpha' }}
    secrets: inherit

  hook-copr:
    uses: ./.github/workflows/hook_copr.yml
    with:
      copr_project: |-
        ${{ contains(github.event.release.name, 'Beta') && 'beta' || contains(github.event.release.name, 'Alpha') && 'alpha' }}
    secrets: inherit

  # Create a PR to bump version when a release is Published
  bump-version:
    if: ${{ github.event.release.published }}
    runs-on: ubuntu-latest
    permissions:
      pull-requests: write
      contents: write
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Setup Python
        uses: actions/setup-python@v5
        with:
          python-version: 3.x

      - name: Get release version string
        run: |
          echo "long=$(./bin/buildinfo.py long)" >> $GITHUB_OUTPUT
          echo "deb=$(./bin/buildinfo.py deb)" >> $GITHUB_OUTPUT
        id: version
        env:
          BUILD_LOCATION: local

      - name: Bump version.properties
        run: >-
          bin/bump_version.py

      - name: Ensure debian deps are installed
        shell: bash
        run: |
          sudo apt-get update -y --fix-missing
          sudo apt-get install -y devscripts

      - name: Update debian changelog
        run: >-
          debian/ci_changelog.sh

      - name: Create version.properties pull request
        uses: peter-evans/create-pull-request@v7
        with:
          title: Bump version.properties
          add-paths: |
            version.properties
            debian/changelog
