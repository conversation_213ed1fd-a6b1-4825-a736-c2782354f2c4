{"editor.formatOnSave": true, "editor.defaultFormatter": "trunk.io", "trunk.enableWindows": true, "files.insertFinalNewline": false, "files.trimFinalNewlines": false, "cmake.configureOnOpen": false, "[cpp]": {"editor.defaultFormatter": "trunk.io"}, "[powershell]": {"editor.defaultFormatter": "ms-vscode.powershell"}, "files.associations": {"*.cpp": "cpp", "cmath": "cpp", "*.tpp": "cpp", "array": "cpp", "atomic": "cpp", "cctype": "cpp", "chrono": "cpp", "clocale": "cpp", "condition_variable": "cpp", "csignal": "cpp", "cstdarg": "cpp", "cstddef": "cpp", "cstdint": "cpp", "cstdio": "cpp", "cstdlib": "cpp", "cstring": "cpp", "ctime": "cpp", "cwchar": "cpp", "cwctype": "cpp", "deque": "cpp", "list": "cpp", "unordered_map": "cpp", "unordered_set": "cpp", "vector": "cpp", "exception": "cpp", "algorithm": "cpp", "functional": "cpp", "iterator": "cpp", "map": "cpp", "memory": "cpp", "memory_resource": "cpp", "numeric": "cpp", "optional": "cpp", "random": "cpp", "ratio": "cpp", "string": "cpp", "string_view": "cpp", "system_error": "cpp", "tuple": "cpp", "type_traits": "cpp", "utility": "cpp", "fstream": "cpp", "initializer_list": "cpp", "iomanip": "cpp", "iosfwd": "cpp", "iostream": "cpp", "istream": "cpp", "limits": "cpp", "mutex": "cpp", "new": "cpp", "ostream": "cpp", "sstream": "cpp", "stdexcept": "cpp", "streambuf": "cpp", "thread": "cpp", "cinttypes": "cpp", "typeinfo": "cpp", "set": "cpp", "variant": "cpp", "debugconfiguration.h": "c", "tls_socket.h": "c"}}