name: Bug Report
description: File a bug report
title: "[Bug]: "
labels: [bug, triage]
body:
  - type: markdown
    attributes:
      value: |
        Thanks for taking the time to fill out this bug report!

  - type: dropdown
    id: category
    attributes:
      label: Category
      description: How would you catagorize this issue?
      multiple: true
      options:
        - Hardware Compatibility
        - BLE
        - Serial
        - WiFi
        - Other
    validations:
      required: true

  - type: dropdown
    id: hardware
    attributes:
      label: Hardware
      description: What hardware are you encountering this issue on?
      multiple: true
      options:
        - Not Applicable
        - T-Beam
        - T-Beam S3
        - T-Beam 0.7
        - T-Lora v1
        - T-Lora v1.3
        - T-Lora v2 1.6
        - T-Deck
        - T-Echo
        - T-Watch
        - Rak4631
        - Rak11200
        - Rak11310
        - Heltec v1
        - Heltec v2
        - Heltec v2.1
        - Heltec V3
        - Heltec Wireless Paper
        - Heltec Wireless Tracker
        - Heltec Mesh Node T114
        - Heltec Vision Master E213
        - Heltec Vision Master E290
        - Heltec Vision Master T190
        - Nano G1
        - Nano G1 Explorer
        - Nano G2 Ultra
        - Raspberry Pi Pico (W)
        - Relay v1
        - Relay v2
        - Seeed Wio Tracker 1110
        - Seeed Card Tracker T1000-E
        - Station G1
        - Station G2
        - unPhone
        - CanaryOne
        - Chatter
        - Linux Native
        - DIY
        - Other
    validations:
      required: true

  - type: checkboxes
    id: mui
    attributes:
      label: Is this bug report about any UI component firmware like InkHUD or Meshtatic UI (MUI)?
      options:
        - label: Meshtastic UI aka MUI colorTFT
        - label: InkHUD ePaper
        - label: OLED slide UI on any display

  - type: input
    id: version
    attributes:
      label: Firmware Version
      description: This can be found on the device's screen or via one of the apps.
      placeholder: x.x.x.yyyyyyy
    validations:
      required: true

  - type: textarea
    id: body
    attributes:
      label: Description
      description: Please provide details on what steps you performed for this to happen.
    validations:
      required: true

  - type: textarea
    id: logs
    attributes:
      label: Relevant log output
      description: If you have any log output to help in diagnosing your bug, please provide it here.
      render: Shell
    validations:
      required: false
