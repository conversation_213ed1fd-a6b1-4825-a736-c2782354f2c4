{"build": {"arduino": {"ldscript": "esp32s3_out.ld", "memory_type": "qio_opi"}, "core": "esp32", "extra_flags": ["-DBOARD_HAS_PSRAM", "-DT_WATCH_S3", "-DARDUINO_USB_CDC_ON_BOOT=1", "-DARDUINO_USB_MODE=0", "-DARDUINO_RUNNING_CORE=1", "-DARDUINO_EVENT_RUNNING_CORE=1"], "f_cpu": "240000000L", "f_flash": "80000000L", "flash_mode": "qio", "hwids": [["0x303A", "0x1001"], ["0x303A", "0x0002"]], "mcu": "esp32s3", "variant": "t-watch-s3"}, "connectivity": ["wifi", "bluetooth"], "debug": {"openocd_target": "esp32s3.cfg"}, "frameworks": ["a<PERSON><PERSON><PERSON>"], "name": "LilyGo T-Watch 2020 V3", "upload": {"flash_size": "8MB", "maximum_ram_size": 327680, "maximum_size": 8388608, "require_upload_port": true, "use_1200bps_touch": true, "wait_for_upload_port": true, "speed": 921600}, "url": "https://www.lilygo.cc/en-pl/products/t-watch-s3", "vendor": "LilyGo"}