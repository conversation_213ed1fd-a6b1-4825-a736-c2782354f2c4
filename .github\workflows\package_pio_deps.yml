name: Package PlatformIO Library Dependencies
# trunk-ignore-all(checkov/CKV_GHA_7): Allow workflow_dispatch inputs for testing

on:
  workflow_call:
    inputs:
      pio_env:
        description: PlatformIO environment to target
        required: true
        type: string
  workflow_dispatch:
    inputs:
      pio_env:
        description: PlatformIO environment to target
        required: true
        type: string

permissions:
  contents: write
  packages: write

jobs:
  pkg-pio-libdeps:
    runs-on: ubuntu-24.04
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          submodules: recursive
          ref: ${{github.event.pull_request.head.ref}}
          repository: ${{github.event.pull_request.head.repo.full_name}}

      - name: Setup Python
        uses: actions/setup-python@v5
        with:
          python-version: 3.x

      - name: Install deps
        shell: bash
        run: |
          pip install platformio

      - name: Get release version string
        run: |
          echo "long=$(./bin/buildinfo.py long)" >> $GITHUB_OUTPUT
        id: version

      - name: Fetch libdeps
        shell: bash
        run: |-
          platformio pkg install -e ${{ inputs.pio_env }}
          platformio pkg install -e ${{ inputs.pio_env }} -t platformio/tool-scons@4.40502.0
        env:
          PLATFORMIO_LIBDEPS_DIR: pio/libdeps
          PLATFORMIO_PACKAGES_DIR: pio/packages
          PLATFORMIO_CORE_DIR: pio/core

      - name: Store binaries as an artifact
        uses: actions/upload-artifact@v4
        with:
          name: platformio-deps-${{ inputs.pio_env }}-${{ steps.version.outputs.long }}
          overwrite: true
          include-hidden-files: true
          path: |
            pio/*
