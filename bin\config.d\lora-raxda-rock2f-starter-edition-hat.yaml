Lora:

### Raxda Rock 2F running Armbian Linux 6.1.99-vendor-rk35xx
### https://github.com/markbirss/rock-2f
### https://github.com/markbirss/lora-starter-edition-sx1262-i2c
### https://github.com/radxa-pkg/radxa-overlays/blob/main/arch/arm64/boot/dts/rockchip/overlays/rk3528-spi0-cs1-spidev.dts
### Require install of https://github.com/radxa-pkg/radxa-overlays and rk3528-spi0-cs1-spidev.dtbo copied to /boot/dtb/rockchip/overlay and enabled 
### in  /boot/armbianEnv.txt - overlays=rk3528-spi0-cs1-spidev
### The Radxa Rock 2F employs multiple gpio chips.
### Each gpio pin must be unique, but can be assigned to a specific gpio chip and line.
### In case solely a no. is given, the default gpio chip and pin == line will be employed.
###
  Module: sx1262  # Radxa Rock 2F + Starter Edition SX1262 HAT by <PERSON>s
  DIO2_AS_RF_SWITCH: true
  DIO3_TCXO_VOLTAGE: 1.8
  spidev: spidev0.1
  CS:             # NSS     PIN_24 -> chip 4, line 14
    pin: 24
    gpiochip: 4
    line: 14
  SCK:            # SCK     PIN_23 -> chip 4, line 12
    pin: 23
    gpiochip: 4
    line: 12
  Busy:           # BUSY    PIN_7 -> chip 4, line 6
    pin: 7
    gpiochip: 4
    line: 6
  MOSI:           # MOSI    PIN_19 -> chip 4, line 10
    pin: 19
    gpiochip: 4
    line: 10
  MISO:           # MISO    PIN_21 -> chip 4, line 11
    pin: 21
    gpiochip: 4
    line: 11
  Reset:          # NRST    PIN_12 -> chip 1, line 13
    pin: 12
    gpiochip: 1
    line: 13
  IRQ:            # DIO1    PIN_15 -> chip 4, line 22
    pin: 15
    gpiochip: 4
    line: 22
#  RXen:           # RXEN    PIN_22 -> chip 3!, line 17
#    pin: 22
#    gpiochip: 3
#    line: 17
#  TXen: RADIOLIB_NC # TXEN   no PIN, no line, fallback to default gpio chip
