/* Automatically generated nanopb constant definitions */
/* Generated by nanopb-0.4.9.1 */

#include "meshtastic/telemetry.pb.h"
#if PB_PROTO_HEADER_VERSION != 40
#error Regenerate this file with the current version of nanopb generator.
#endif

PB_BIND(meshtastic_DeviceMetrics, meshtastic_DeviceMetrics, AUTO)


PB_BIND(meshtastic_EnvironmentMetrics, meshtastic_EnvironmentMetrics, AUTO)


PB_BIND(meshtastic_PowerMetrics, meshtastic_PowerMetrics, AUTO)


PB_BIND(meshtastic_PowerMetrics_Location, meshtastic_PowerMetrics_Location, AUTO)


PB_BIND(meshtastic_AirQualityMetrics, meshtastic_AirQualityMetrics, AUTO)


PB_BIND(meshtastic_LocalStats, meshtastic_LocalStats, AUTO)


PB_BIND(meshtastic_HealthMetrics, meshtastic_HealthMetrics, AUTO)


PB_BIND(meshtastic_Telemetry, meshtastic_Telemetry, AUTO)


PB_BIND(meshtastic_Nau7802Config, meshtastic_Nau7802Config, AUTO)







