[nrf52840_base]
extends = nrf52_base

build_flags = ${nrf52_base.build_flags} 

lib_deps =
  ${nrf52_base.lib_deps}
  ${environmental_base.lib_deps}
  https://github.com/Kongduino/Adafruit_nRFCrypto/archive/e31a8825ea3300b163a0a3c1ddd5de34e10e1371.zip

; Common NRF52 debugging settings follow.  See the Meshtastic developer docs for how to connect SWD debugging probes to your board.

; We want the initial breakpoint at setup() instead of main().  Also we want to enable semihosting at that point so instead of
debug_init_break = tbreak setup
; we just turn off the platformio tbreak and do it in .gdbinit (where we have more flexibility for scripting)
; also we use a permanent breakpoint so it gets reused each time we restart the debugging session?
; debug_init_break = tbreak main

; Note: add "monitor arm semihosting_redirect tcp 4444 all" if you want the stdout from the device to go to that port number instead
; (for use by meshtastic command line)
;  monitor arm semihosting disable
;  monitor debug_level 3
;
; IMPORTANT: fileio must be disabled before using port 5555 - openocd ver 0.12 has a bug where if enabled it never properly parses the special :tt name
; for stdio access.
;   monitor arm semihosting_redirect tcp 5555 stdio

; Also note: it is _impossible_ to do non blocking reads on the semihost console port (an oversight when ARM specified the semihost API).
; So we'll neve be able to general purpose bi-directional communication with the device over semihosting.
debug_extra_cmds =
  echo Running .gdbinit script
  ;monitor arm semihosting enable
  ;monitor arm semihosting_fileio enable
  ;monitor arm semihosting_redirect disable
  commands 1
  ; echo Breakpoint at setup() has semihosting console, connect to it with "telnet localhost 5555"
  ; set wantSemihost = 1
  set useSoftDevice = 0
  end

  ; Only reprogram the board if the code has changed
debug_load_mode = modified
;debug_load_mode = manual
; We default to the stlink adapter because it is very cheap and works well, though others (such as jlink) are also supported.
;debug_tool = jlink
debug_tool = stlink
debug_speed = 4000
;debug_tool = custom
; debug_server =
;  openocd
;  -f
;  /usr/local/share/openocd/scripts/interface/stlink.cfg
;  -f
;  /usr/local/share/openocd/scripts/target/nrf52.cfg
; $PLATFORMIO_CORE_DIR/packages/tool-openocd/openocd/scripts/interface/cmsis-dap.cfg

; Allows programming and debug via the RAK NanoDAP as the default debugger tool for the RAK4631 (it is only $10!)
; programming time is about the same as the bootloader version.
; For information on this see the meshtastic developers documentation for "Development on the NRF52"
; We manually pass in the elf file so that pyocd can reverse engineer FreeRTOS data (running threads, etc...)
;debug_server =
;  pyocd
;  gdbserver
;  -j
;  ${platformio.workspace_dir}/..
;  -t
;  nrf52840
;  --semihosting
;  --elf
;  ${platformio.build_dir}/${this.__env__}/firmware.elf

; If you want to debug the semihosting support you can turn on extra logging in pyocd with
;   -L
;   pyocd.debug.semihost.trace=debug

; The following is not needed because it automatically tries do this
;debug_server_ready_pattern = -.*GDB server started on port \d+.*
;debug_port = localhost:3333