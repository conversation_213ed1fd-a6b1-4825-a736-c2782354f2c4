name: Update protobufs and regenerate classes
on: workflow_dispatch

permissions: read-all

jobs:
  update-protobufs:
    runs-on: ubuntu-latest
    permissions:
      contents: write
      pull-requests: write
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          submodules: true

      - name: Update submodule
        if: ${{ github.ref == 'refs/heads/master' }}
        run: |
          git submodule update --remote protobufs

      - name: Download nanopb
        run: |
          wget https://jpa.kapsi.fi/nanopb/download/nanopb-0.4.9.1-linux-x86.tar.gz
          tar xvzf nanopb-0.4.9.1-linux-x86.tar.gz
          mv nanopb-0.4.9.1-linux-x86 nanopb-0.4.9

      - name: Re-generate protocol buffers
        run: |
          ./bin/regen-protos.sh

      - name: Create pull request
        uses: peter-evans/create-pull-request@v7
        with:
          title: Update protobufs and classes
          add-paths: |
            protobufs
            src/mesh
