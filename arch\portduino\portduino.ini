; The Portduino based 'native' environment. Currently supported on Linux targets with real LoRa hardware (or simulated).
[portduino_base]
platform = https://github.com/meshtastic/platform-native/archive/c5bd469ab9b5a6966321e09557b27d906961da63.zip
framework = arduino

build_src_filter = 
  ${env.build_src_filter} 
  -<platform/esp32/> 
  -<nimble/> 
  -<platform/nrf52/> 
  -<platform/stm32wl/> 
  -<platform/rp2xx0>
  -<mesh/wifi/>
  -<mesh/http/>
  +<mesh/raspihttp/>
  -<mesh/eth/>
  -<modules/esp32>
  -<modules/Telemetry/EnvironmentTelemetry.cpp>
  -<modules/Telemetry/AirQualityTelemetry.cpp>
  -<modules/Telemetry/Sensor>
  +<../variants/portduino>

lib_deps =
  ${env.lib_deps}
  ${networking_base.lib_deps}
  ${radiolib_base.lib_deps}
  rweather/Crypto@^0.4.0
  lovyan03/LovyanGFX@^1.2.0
  https://github.com/pine64/libch341-spi-userspace/archive/a9b17e3452f7fb747000d9b4ad4409155b39f6ef.zip

build_flags =
  ${arduino_base.build_flags}
  -fPIC
  -Isrc/platform/portduino
  -DRADIOLIB_EEPROM_UNSUPPORTED
  -DPORTDUINO_LINUX_HARDWARE
  -DHAS_UDP_MULTICAST
  -lpthread
  -lstdc++fs
  -lbluetooth
  -lgpiod
  -lyaml-cpp
  -li2c
  -luv
  -std=c++17
